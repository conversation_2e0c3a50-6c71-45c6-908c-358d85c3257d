"""
市场分析模块
用于分析日K线数据，识别市场状态和趋势，生成每日交易计划
"""

import os
import pandas as pd
import numpy as np
import talib
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import mplfinance as mpf
import json

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('market_analysis.log')
    ]
)
logger = logging.getLogger("MarketAnalysis")

class MarketAnalyzer:
    """市场分析器"""

    def __init__(self, config_file="market_analysis_config.json"):
        """初始化"""
        self.config = self._load_config(config_file)
        self.data = None
        self.analysis_results = {}
        self.trade_plan = {}

        # 加载中文字体
        try:
            self.font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf")
        except:
            self.font = None
            logger.warning("无法加载中文字体，图表中的中文可能显示为乱码")

        logger.info("市场分析器初始化完成")

    def _load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "data_file": "PSFUTURES2025.csv",  # 历史数据文件
            "output_dir": "analysis_reports",   # 输出目录
            "technical_indicators": {
                "ma_periods": [5, 10, 20, 60],  # 移动平均线周期
                "rsi_period": 14,               # RSI周期
                "macd_params": [12, 26, 9],     # MACD参数
                "boll_period": 20,              # 布林带周期
                "atr_period": 14,               # ATR周期
                "kd_params": [9, 3, 3],         # KDJ参数
            },
            "overbought_oversold": {
                "rsi_overbought": 70,           # RSI超买阈值
                "rsi_oversold": 30,             # RSI超卖阈值
                "kd_overbought": 80,            # KD超买阈值
                "kd_oversold": 20,              # KD超卖阈值
            },
            "trend_thresholds": {
                "strong_uptrend": 0.05,         # 强上升趋势阈值
                "uptrend": 0.02,                # 上升趋势阈值
                "downtrend": -0.02,             # 下降趋势阈值
                "strong_downtrend": -0.05,      # 强下降趋势阈值
            },
            "fundamental_data": {
                "supply": 1000000,              # 供应量（吨）
                "demand": 800000,               # 需求量（吨）
                "inventory": 400000,            # 库存量（吨）
                "production_cost": 30000,       # 生产成本（元/吨）
            },
            "position_management": {
                "max_position": 10,             # 最大持仓（手）
                "risk_per_trade": 2.0,          # 单笔风险（%）
                "stop_loss_atr_multiple": 2.5,  # 止损ATR倍数
                "take_profit_levels": [         # 止盈级别
                    {"percent": 0.3, "atr_multiple": 1.5},
                    {"percent": 0.3, "atr_multiple": 2.5},
                    {"percent": 0.2, "atr_multiple": 4.0}
                ]
            }
        }

        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"成功加载配置文件: {config_file}")
                return config
            else:
                logger.warning(f"配置文件不存在: {config_file}，使用默认配置")
                return default_config
        except Exception as e:
            logger.error(f"加载配置文件出错: {e}，使用默认配置")
            return default_config

    def load_data(self, data_file=None):
        """加载历史数据"""
        try:
            file_path = data_file or self.config.get("data_file")
            if not file_path:
                logger.error("未指定数据文件")
                return False

            if not os.path.exists(file_path):
                logger.error(f"数据文件不存在: {file_path}")
                return False

            # 读取CSV文件
            self.data = pd.read_csv(file_path, encoding='utf-8', skiprows=1)

            # 确保数值列为浮点型
            numeric_columns = ['开盘价', '最高价', '最低价', '收盘价', '前结算价', '结算价', '涨跌', '涨跌1', '成交量', '持仓量']
            for col in numeric_columns:
                if col in self.data.columns:
                    self.data[col] = pd.to_numeric(self.data[col], errors='coerce')

            # 按日期排序
            self.data = self.data.sort_values(by='交易日期')

            # 重命名列以便使用talib
            if '收盘价' in self.data.columns:
                self.data['close'] = self.data['收盘价']
            if '开盘价' in self.data.columns:
                self.data['open'] = self.data['开盘价']
            if '最高价' in self.data.columns:
                self.data['high'] = self.data['最高价']
            if '最低价' in self.data.columns:
                self.data['low'] = self.data['最低价']
            if '成交量' in self.data.columns:
                self.data['volume'] = self.data['成交量']

            logger.info(f"成功加载数据，共{len(self.data)}条记录")
            return True
        except Exception as e:
            logger.error(f"加载数据出错: {e}")
            return False

    def calculate_indicators(self):
        """计算技术指标"""
        if self.data is None or self.data.empty:
            logger.error("数据为空，无法计算指标")
            return False

        try:
            # 获取配置
            tech_config = self.config.get("technical_indicators", {})

            # 计算移动平均线
            ma_periods = tech_config.get("ma_periods", [5, 10, 20, 60])
            for period in ma_periods:
                self.data[f'MA{period}'] = talib.SMA(self.data['close'], timeperiod=period)

            # 计算RSI
            rsi_period = tech_config.get("rsi_period", 14)
            self.data['RSI'] = talib.RSI(self.data['close'], timeperiod=rsi_period)

            # 计算MACD
            macd_params = tech_config.get("macd_params", [12, 26, 9])
            macd, macd_signal, macd_hist = talib.MACD(
                self.data['close'],
                fastperiod=macd_params[0],
                slowperiod=macd_params[1],
                signalperiod=macd_params[2]
            )
            self.data['MACD'] = macd
            self.data['MACD_SIGNAL'] = macd_signal
            self.data['MACD_HIST'] = macd_hist

            # 计算布林带
            boll_period = tech_config.get("boll_period", 20)
            upper, middle, lower = talib.BBANDS(
                self.data['close'],
                timeperiod=boll_period,
                nbdevup=2,
                nbdevdn=2,
                matype=0
            )
            self.data['BOLL_UPPER'] = upper
            self.data['BOLL_MIDDLE'] = middle
            self.data['BOLL_LOWER'] = lower

            # 计算ATR
            atr_period = tech_config.get("atr_period", 14)
            self.data['ATR'] = talib.ATR(
                self.data['high'],
                self.data['low'],
                self.data['close'],
                timeperiod=atr_period
            )

            # 计算KDJ
            kd_params = tech_config.get("kd_params", [9, 3, 3])
            self.data['K'], self.data['D'] = talib.STOCH(
                self.data['high'],
                self.data['low'],
                self.data['close'],
                fastk_period=kd_params[0],
                slowk_period=kd_params[1],
                slowk_matype=0,
                slowd_period=kd_params[2],
                slowd_matype=0
            )
            self.data['J'] = 3 * self.data['K'] - 2 * self.data['D']

            # 计算DMI
            self.data['PLUS_DI'] = talib.PLUS_DI(self.data['high'], self.data['low'], self.data['close'], timeperiod=14)
            self.data['MINUS_DI'] = talib.MINUS_DI(self.data['high'], self.data['low'], self.data['close'], timeperiod=14)
            self.data['ADX'] = talib.ADX(self.data['high'], self.data['low'], self.data['close'], timeperiod=14)

            # 计算CCI
            self.data['CCI'] = talib.CCI(self.data['high'], self.data['low'], self.data['close'], timeperiod=14)

            # 计算OBV
            self.data['OBV'] = talib.OBV(self.data['close'], self.data['volume'])

            logger.info("成功计算技术指标")
            return True
        except Exception as e:
            logger.error(f"计算技术指标出错: {e}")
            return False

    def analyze_market(self):
        """分析市场状态"""
        if self.data is None or self.data.empty:
            logger.error("数据为空，无法分析市场")
            return False

        try:
            # 获取最新数据
            latest_data = self.data.iloc[-1]
            prev_data = self.data.iloc[-2]

            # 获取配置
            overbought_oversold = self.config.get("overbought_oversold", {})
            trend_thresholds = self.config.get("trend_thresholds", {})

            # 初始化分析结果
            self.analysis_results = {
                "date": latest_data.get('交易日期', datetime.now().strftime('%Y%m%d')),
                "price": {
                    "current": latest_data['close'],
                    "open": latest_data['open'],
                    "high": latest_data['high'],
                    "low": latest_data['low'],
                    "prev_close": prev_data['close'],
                    "change": latest_data['close'] - prev_data['close'],
                    "change_percent": (latest_data['close'] - prev_data['close']) / prev_data['close'] * 100
                },
                "volume": {
                    "current": latest_data['volume'],
                    "prev": prev_data['volume'],
                    "change_percent": (latest_data['volume'] - prev_data['volume']) / prev_data['volume'] * 100 if prev_data['volume'] > 0 else 0
                },
                "indicators": {
                    "ma": {f"ma{period}": latest_data.get(f'MA{period}', None) for period in self.config.get("technical_indicators", {}).get("ma_periods", [5, 10, 20, 60])},
                    "rsi": latest_data['RSI'],
                    "macd": {
                        "macd": latest_data['MACD'],
                        "signal": latest_data['MACD_SIGNAL'],
                        "hist": latest_data['MACD_HIST']
                    },
                    "boll": {
                        "upper": latest_data['BOLL_UPPER'],
                        "middle": latest_data['BOLL_MIDDLE'],
                        "lower": latest_data['BOLL_LOWER']
                    },
                    "atr": latest_data['ATR'],
                    "kdj": {
                        "k": latest_data['K'],
                        "d": latest_data['D'],
                        "j": latest_data['J']
                    },
                    "dmi": {
                        "plus_di": latest_data['PLUS_DI'],
                        "minus_di": latest_data['MINUS_DI'],
                        "adx": latest_data['ADX']
                    },
                    "cci": latest_data['CCI'],
                    "obv": latest_data['OBV']
                },
                "market_status": {},
                "trend": {},
                "signals": {},
                "support_resistance": {}
            }

            # 分析市场状态
            # 1. 超买/超卖状态
            rsi = latest_data['RSI']
            k = latest_data['K']
            d = latest_data['D']

            if rsi > overbought_oversold.get("rsi_overbought", 70):
                self.analysis_results["market_status"]["rsi"] = "超买"
            elif rsi < overbought_oversold.get("rsi_oversold", 30):
                self.analysis_results["market_status"]["rsi"] = "超卖"
            else:
                self.analysis_results["market_status"]["rsi"] = "中性"

            if k > overbought_oversold.get("kd_overbought", 80) and d > overbought_oversold.get("kd_overbought", 80):
                self.analysis_results["market_status"]["kdj"] = "超买"
            elif k < overbought_oversold.get("kd_oversold", 20) and d < overbought_oversold.get("kd_oversold", 20):
                self.analysis_results["market_status"]["kdj"] = "超卖"
            else:
                self.analysis_results["market_status"]["kdj"] = "中性"

            # 2. 趋势分析
            # 短期趋势（5日均线与10日均线的关系）
            ma5 = latest_data.get('MA5')
            ma10 = latest_data.get('MA10')
            ma20 = latest_data.get('MA20')
            ma60 = latest_data.get('MA60')

            if ma5 is not None and ma10 is not None:
                if ma5 > ma10:
                    self.analysis_results["trend"]["short_term"] = "上升"
                else:
                    self.analysis_results["trend"]["short_term"] = "下降"

            # 中期趋势（10日均线与20日均线的关系）
            if ma10 is not None and ma20 is not None:
                if ma10 > ma20:
                    self.analysis_results["trend"]["medium_term"] = "上升"
                else:
                    self.analysis_results["trend"]["medium_term"] = "下降"

            # 长期趋势（20日均线与60日均线的关系）
            if ma20 is not None and ma60 is not None:
                if ma20 > ma60:
                    self.analysis_results["trend"]["long_term"] = "上升"
                else:
                    self.analysis_results["trend"]["long_term"] = "下降"

            # 3. 信号分析
            # MACD信号
            macd = latest_data['MACD']
            macd_signal = latest_data['MACD_SIGNAL']
            macd_hist = latest_data['MACD_HIST']
            prev_macd_hist = self.data.iloc[-2]['MACD_HIST']

            if macd > macd_signal and prev_macd_hist < 0 and macd_hist > 0:
                self.analysis_results["signals"]["macd"] = "金叉（买入）"
            elif macd < macd_signal and prev_macd_hist > 0 and macd_hist < 0:
                self.analysis_results["signals"]["macd"] = "死叉（卖出）"
            elif macd_hist > 0:
                self.analysis_results["signals"]["macd"] = "多头"
            else:
                self.analysis_results["signals"]["macd"] = "空头"

            # KDJ信号
            k = latest_data['K']
            d = latest_data['D']
            prev_k = self.data.iloc[-2]['K']
            prev_d = self.data.iloc[-2]['D']

            if k > d and prev_k <= prev_d:
                self.analysis_results["signals"]["kdj"] = "金叉（买入）"
            elif k < d and prev_k >= prev_d:
                self.analysis_results["signals"]["kdj"] = "死叉（卖出）"
            elif k > d:
                self.analysis_results["signals"]["kdj"] = "多头"
            else:
                self.analysis_results["signals"]["kdj"] = "空头"

            # DMI信号
            plus_di = latest_data['PLUS_DI']
            minus_di = latest_data['MINUS_DI']
            adx = latest_data['ADX']
            prev_plus_di = self.data.iloc[-2]['PLUS_DI']
            prev_minus_di = self.data.iloc[-2]['MINUS_DI']

            if plus_di > minus_di and prev_plus_di <= prev_minus_di:
                self.analysis_results["signals"]["dmi"] = "金叉（买入）"
            elif plus_di < minus_di and prev_plus_di >= prev_minus_di:
                self.analysis_results["signals"]["dmi"] = "死叉（卖出）"
            elif plus_di > minus_di:
                self.analysis_results["signals"]["dmi"] = "多头"
                if adx > 25:
                    self.analysis_results["signals"]["dmi"] += "（强）"
            else:
                self.analysis_results["signals"]["dmi"] = "空头"
                if adx > 25:
                    self.analysis_results["signals"]["dmi"] += "（强）"

            # 4. 支撑位和阻力位分析
            # 使用布林带作为支撑位和阻力位
            self.analysis_results["support_resistance"]["boll_upper"] = latest_data['BOLL_UPPER']
            self.analysis_results["support_resistance"]["boll_middle"] = latest_data['BOLL_MIDDLE']
            self.analysis_results["support_resistance"]["boll_lower"] = latest_data['BOLL_LOWER']

            # 使用前期高点和低点作为支撑位和阻力位
            recent_data = self.data.iloc[-20:]  # 最近20个交易日
            recent_highs = recent_data['high'].nlargest(3)
            recent_lows = recent_data['low'].nsmallest(3)

            self.analysis_results["support_resistance"]["recent_highs"] = recent_highs.tolist()
            self.analysis_results["support_resistance"]["recent_lows"] = recent_lows.tolist()

            # 5. 综合评估
            # 根据各项指标的信号，给出综合评估
            bullish_signals = 0
            bearish_signals = 0

            # 计算看多信号数量
            if self.analysis_results["market_status"]["rsi"] == "超卖":
                bullish_signals += 1
            if self.analysis_results["market_status"]["kdj"] == "超卖":
                bullish_signals += 1
            if self.analysis_results["signals"]["macd"] in ["金叉（买入）", "多头"]:
                bullish_signals += 1
            if self.analysis_results["signals"]["kdj"] in ["金叉（买入）", "多头"]:
                bullish_signals += 1
            if self.analysis_results["signals"]["dmi"] in ["金叉（买入）", "多头", "多头（强）"]:
                bullish_signals += 1
            if self.analysis_results["trend"]["short_term"] == "上升":
                bullish_signals += 1

            # 计算看空信号数量
            if self.analysis_results["market_status"]["rsi"] == "超买":
                bearish_signals += 1
            if self.analysis_results["market_status"]["kdj"] == "超买":
                bearish_signals += 1
            if self.analysis_results["signals"]["macd"] in ["死叉（卖出）", "空头"]:
                bearish_signals += 1
            if self.analysis_results["signals"]["kdj"] in ["死叉（卖出）", "空头"]:
                bearish_signals += 1
            if self.analysis_results["signals"]["dmi"] in ["死叉（卖出）", "空头", "空头（强）"]:
                bearish_signals += 1
            if self.analysis_results["trend"]["short_term"] == "下降":
                bearish_signals += 1

            # 综合评估
            self.analysis_results["overall_assessment"] = {
                "bullish_signals": bullish_signals,
                "bearish_signals": bearish_signals,
                "neutral_signals": 6 - bullish_signals - bearish_signals,  # 假设总共有6个信号
                "conclusion": ""
            }

            if bullish_signals > bearish_signals + 2:
                self.analysis_results["overall_assessment"]["conclusion"] = "强烈看多"
            elif bullish_signals > bearish_signals:
                self.analysis_results["overall_assessment"]["conclusion"] = "偏多"
            elif bearish_signals > bullish_signals + 2:
                self.analysis_results["overall_assessment"]["conclusion"] = "强烈看空"
            elif bearish_signals > bullish_signals:
                self.analysis_results["overall_assessment"]["conclusion"] = "偏空"
            else:
                self.analysis_results["overall_assessment"]["conclusion"] = "中性"

            logger.info("成功分析市场状态")
            return True
        except Exception as e:
            logger.error(f"分析市场状态出错: {e}")
            return False

    def generate_trade_plan(self, current_position=None):
        """生成交易计划"""
        if not self.analysis_results:
            logger.error("分析结果为空，无法生成交易计划")
            return False

        try:
            # 获取配置
            position_config = self.config.get("position_management", {})
            max_position = position_config.get("max_position", 10)
            risk_per_trade = position_config.get("risk_per_trade", 2.0)
            stop_loss_atr_multiple = position_config.get("stop_loss_atr_multiple", 2.5)
            take_profit_levels = position_config.get("take_profit_levels", [])

            # 获取最新数据
            latest_data = self.data.iloc[-1]
            current_price = latest_data['close']
            current_atr = latest_data['ATR']

            # 初始化交易计划
            self.trade_plan = {
                "date": self.analysis_results["date"],
                "market_bias": self.analysis_results["overall_assessment"]["conclusion"],
                "current_price": current_price,
                "current_position": current_position or {"direction": "NONE", "size": 0, "entry_price": 0},
                "atr": current_atr,
                "stop_loss": {},
                "take_profit": {},
                "entry_zones": {},
                "position_adjustment": {},
                "risk_management": {}
            }

            # 根据市场偏向设置交易方向
            market_bias = self.analysis_results["overall_assessment"]["conclusion"]
            if market_bias in ["强烈看空", "偏空"]:
                trade_direction = "SHORT"
            elif market_bias in ["强烈看多", "偏多"]:
                trade_direction = "LONG"
            else:
                trade_direction = "NEUTRAL"

            self.trade_plan["recommended_direction"] = trade_direction

            # 计算止损位
            if trade_direction == "SHORT":
                stop_loss_price = current_price + (current_atr * stop_loss_atr_multiple)
                stop_loss_points = stop_loss_price - current_price
            elif trade_direction == "LONG":
                stop_loss_price = current_price - (current_atr * stop_loss_atr_multiple)
                stop_loss_points = current_price - stop_loss_price
            else:
                stop_loss_price = None
                stop_loss_points = None

            self.trade_plan["stop_loss"] = {
                "price": stop_loss_price,
                "points": stop_loss_points,
                "atr_multiple": stop_loss_atr_multiple
            }

            # 计算止盈位
            take_profit_prices = []
            if trade_direction == "SHORT":
                for level in take_profit_levels:
                    price = current_price - (current_atr * level.get("atr_multiple", 1.5))
                    take_profit_prices.append({
                        "percent": level.get("percent", 0.3) * 100,
                        "price": price,
                        "points": current_price - price,
                        "atr_multiple": level.get("atr_multiple", 1.5)
                    })
            elif trade_direction == "LONG":
                for level in take_profit_levels:
                    price = current_price + (current_atr * level.get("atr_multiple", 1.5))
                    take_profit_prices.append({
                        "percent": level.get("percent", 0.3) * 100,
                        "price": price,
                        "points": price - current_price,
                        "atr_multiple": level.get("atr_multiple", 1.5)
                    })

            self.trade_plan["take_profit"] = take_profit_prices

            # 计算入场区域
            if trade_direction == "SHORT":
                # 做空入场区域：当前价格到阻力位之间
                resistance = min(
                    self.analysis_results["support_resistance"]["boll_upper"],
                    max(self.analysis_results["support_resistance"]["recent_highs"]) if self.analysis_results["support_resistance"]["recent_highs"] else float('inf')
                )
                entry_zone_low = current_price
                entry_zone_high = min(resistance, current_price * 1.02)  # 最高不超过当前价格的2%
            elif trade_direction == "LONG":
                # 做多入场区域：支撑位到当前价格之间
                support = max(
                    self.analysis_results["support_resistance"]["boll_lower"],
                    min(self.analysis_results["support_resistance"]["recent_lows"]) if self.analysis_results["support_resistance"]["recent_lows"] else 0
                )
                entry_zone_low = max(support, current_price * 0.98)  # 最低不低于当前价格的2%
                entry_zone_high = current_price
            else:
                entry_zone_low = None
                entry_zone_high = None

            self.trade_plan["entry_zones"] = {
                "low": entry_zone_low,
                "high": entry_zone_high,
                "ideal": (entry_zone_low + entry_zone_high) / 2 if entry_zone_low and entry_zone_high else None
            }

            # 计算仓位调整建议
            current_direction = current_position["direction"] if current_position else "NONE"
            current_size = current_position["size"] if current_position else 0

            if current_direction == "NONE" or current_size == 0:
                # 无持仓，根据市场偏向建议新开仓
                if trade_direction == "SHORT":
                    position_adjustment = "开空仓"
                    target_size = max_position // 2  # 建议开半仓
                elif trade_direction == "LONG":
                    position_adjustment = "开多仓"
                    target_size = max_position // 2  # 建议开半仓
                else:
                    position_adjustment = "观望"
                    target_size = 0
            elif current_direction == trade_direction:
                # 持仓方向与市场偏向一致
                if (trade_direction == "SHORT" and market_bias == "强烈看空") or (trade_direction == "LONG" and market_bias == "强烈看多"):
                    # 强烈信号，可以加仓
                    if current_size < max_position:
                        position_adjustment = "加仓"
                        target_size = min(current_size + max_position // 4, max_position)  # 加1/4仓，但不超过最大仓位
                    else:
                        position_adjustment = "持仓不变"
                        target_size = current_size
                else:
                    # 一般信号，保持仓位不变
                    position_adjustment = "持仓不变"
                    target_size = current_size
            else:
                # 持仓方向与市场偏向相反
                if (current_direction == "SHORT" and market_bias == "强烈看多") or (current_direction == "LONG" and market_bias == "强烈看空"):
                    # 强烈反向信号，建议减仓或平仓
                    position_adjustment = "平仓"
                    target_size = 0
                elif (current_direction == "SHORT" and market_bias == "偏多") or (current_direction == "LONG" and market_bias == "偏空"):
                    # 一般反向信号，建议减仓
                    position_adjustment = "减仓"
                    target_size = current_size // 2  # 减半仓
                else:
                    # 中性信号，保持仓位不变
                    position_adjustment = "持仓不变"
                    target_size = current_size

            self.trade_plan["position_adjustment"] = {
                "recommendation": position_adjustment,
                "current_size": current_size,
                "target_size": target_size,
                "size_change": target_size - current_size
            }

            # 风险管理
            account_balance = 1000000  # 假设账户余额100万，实际应从配置或参数获取
            risk_amount = account_balance * risk_per_trade / 100

            if stop_loss_points and stop_loss_points > 0:
                # 计算每点价值
                point_value = 10  # 假设每手每点10元，需根据实际合约规格调整

                # 计算最大仓位
                max_risk_position = risk_amount / (stop_loss_points * point_value)
                max_risk_position = int(max_risk_position)  # 向下取整

                # 确保不超过最大允许仓位
                max_risk_position = min(max_risk_position, max_position)
            else:
                max_risk_position = 0

            self.trade_plan["risk_management"] = {
                "account_balance": account_balance,
                "risk_amount": risk_amount,
                "risk_percentage": risk_per_trade,
                "max_risk_position": max_risk_position
            }

            logger.info("成功生成交易计划")
            return True
        except Exception as e:
            logger.error(f"生成交易计划出错: {e}")
            return False

    def generate_report(self, output_file=None, include_charts=True):
        """生成分析报告"""
        if not self.analysis_results or not self.trade_plan:
            logger.error("分析结果或交易计划为空，无法生成报告")
            return False

        try:
            # 创建输出目录
            output_dir = self.config.get("output_dir", "analysis_reports")
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 设置输出文件名
            if not output_file:
                date_str = self.analysis_results["date"]
                output_file = os.path.join(output_dir, f"market_analysis_{date_str}.md")

            # 生成报告内容
            report = []

            # 标题
            report.append(f"# 多晶硅期货市场分析报告")
            report.append(f"## 日期: {self.analysis_results['date']}")
            report.append("")

            # 市场概况
            report.append("## 市场概况")
            price_info = self.analysis_results["price"]
            report.append(f"- **当前价格**: {price_info['current']:.2f}")
            report.append(f"- **开盘价**: {price_info['open']:.2f}")
            report.append(f"- **最高价**: {price_info['high']:.2f}")
            report.append(f"- **最低价**: {price_info['low']:.2f}")
            report.append(f"- **前收盘价**: {price_info['prev_close']:.2f}")
            report.append(f"- **涨跌**: {price_info['change']:.2f} ({price_info['change_percent']:.2f}%)")
            report.append(f"- **成交量**: {self.analysis_results['volume']['current']:.0f} (较前日变化: {self.analysis_results['volume']['change_percent']:.2f}%)")
            report.append("")

            # 技术指标
            report.append("## 技术指标")

            # 移动平均线
            ma_info = self.analysis_results["indicators"]["ma"]
            report.append("### 移动平均线")
            for period, value in ma_info.items():
                if value is not None:
                    report.append(f"- **{period.upper()}**: {value:.2f}")
            report.append("")

            # RSI
            rsi = self.analysis_results["indicators"]["rsi"]
            rsi_status = self.analysis_results["market_status"]["rsi"]
            report.append("### RSI")
            report.append(f"- **RSI(14)**: {rsi:.2f} ({rsi_status})")
            report.append("")

            # MACD
            macd_info = self.analysis_results["indicators"]["macd"]
            macd_signal = self.analysis_results["signals"]["macd"]
            report.append("### MACD")
            report.append(f"- **MACD**: {macd_info['macd']:.2f}")
            report.append(f"- **Signal**: {macd_info['signal']:.2f}")
            report.append(f"- **Histogram**: {macd_info['hist']:.2f}")
            report.append(f"- **信号**: {macd_signal}")
            report.append("")

            # KDJ
            kdj_info = self.analysis_results["indicators"]["kdj"]
            kdj_status = self.analysis_results["market_status"]["kdj"]
            kdj_signal = self.analysis_results["signals"]["kdj"]
            report.append("### KDJ")
            report.append(f"- **K**: {kdj_info['k']:.2f}")
            report.append(f"- **D**: {kdj_info['d']:.2f}")
            report.append(f"- **J**: {kdj_info['j']:.2f}")
            report.append(f"- **状态**: {kdj_status}")
            report.append(f"- **信号**: {kdj_signal}")
            report.append("")

            # 布林带
            boll_info = self.analysis_results["indicators"]["boll"]
            report.append("### 布林带")
            report.append(f"- **上轨**: {boll_info['upper']:.2f}")
            report.append(f"- **中轨**: {boll_info['middle']:.2f}")
            report.append(f"- **下轨**: {boll_info['lower']:.2f}")
            report.append("")

            # ATR
            atr = self.analysis_results["indicators"]["atr"]
            report.append("### ATR")
            report.append(f"- **ATR(14)**: {atr:.2f}")
            report.append("")

            # 趋势分析
            trend_info = self.analysis_results["trend"]
            report.append("## 趋势分析")
            report.append(f"- **短期趋势**: {trend_info.get('short_term', '未知')}")
            report.append(f"- **中期趋势**: {trend_info.get('medium_term', '未知')}")
            report.append(f"- **长期趋势**: {trend_info.get('long_term', '未知')}")
            report.append("")

            # 支撑位和阻力位
            sr_info = self.analysis_results["support_resistance"]
            report.append("## 支撑位和阻力位")
            report.append("### 布林带")
            report.append(f"- **上轨(阻力)**: {sr_info['boll_upper']:.2f}")
            report.append(f"- **中轨**: {sr_info['boll_middle']:.2f}")
            report.append(f"- **下轨(支撑)**: {sr_info['boll_lower']:.2f}")
            report.append("")

            report.append("### 历史高点(阻力)")
            for high in sr_info["recent_highs"]:
                report.append(f"- {high:.2f}")
            report.append("")

            report.append("### 历史低点(支撑)")
            for low in sr_info["recent_lows"]:
                report.append(f"- {low:.2f}")
            report.append("")

            # 综合评估
            assessment = self.analysis_results["overall_assessment"]
            report.append("## 综合评估")
            report.append(f"- **看多信号**: {assessment['bullish_signals']}")
            report.append(f"- **看空信号**: {assessment['bearish_signals']}")
            report.append(f"- **中性信号**: {assessment['neutral_signals']}")
            report.append(f"- **结论**: {assessment['conclusion']}")
            report.append("")

            # 交易计划
            report.append("## 交易计划")
            report.append(f"- **市场偏向**: {self.trade_plan['market_bias']}")
            report.append(f"- **建议方向**: {self.trade_plan['recommended_direction']}")
            report.append(f"- **当前价格**: {self.trade_plan['current_price']:.2f}")
            report.append("")

            # 入场区域
            entry_zones = self.trade_plan["entry_zones"]
            if entry_zones["low"] and entry_zones["high"]:
                report.append("### 入场区域")
                report.append(f"- **区间**: {entry_zones['low']:.2f} - {entry_zones['high']:.2f}")
                report.append(f"- **理想入场点**: {entry_zones['ideal']:.2f}")
                report.append("")

            # 止损设置
            stop_loss = self.trade_plan["stop_loss"]
            if stop_loss["price"]:
                report.append("### 止损设置")
                report.append(f"- **止损价**: {stop_loss['price']:.2f}")
                report.append(f"- **止损点数**: {stop_loss['points']:.2f}")
                report.append(f"- **ATR倍数**: {stop_loss['atr_multiple']:.2f}")
                report.append("")

            # 止盈设置
            take_profit = self.trade_plan["take_profit"]
            if take_profit:
                report.append("### 止盈设置")
                for level in take_profit:
                    report.append(f"- **{level['percent']:.0f}%仓位**: {level['price']:.2f} (距离: {level['points']:.2f}点, {level['atr_multiple']}×ATR)")
                report.append("")

            # 仓位调整
            position_adj = self.trade_plan["position_adjustment"]
            report.append("### 仓位调整建议")
            report.append(f"- **建议**: {position_adj['recommendation']}")
            report.append(f"- **当前仓位**: {position_adj['current_size']}手")
            report.append(f"- **目标仓位**: {position_adj['target_size']}手")
            report.append(f"- **调整量**: {position_adj['size_change']}手")
            report.append("")

            # 风险管理
            risk_mgmt = self.trade_plan["risk_management"]
            report.append("### 风险管理")
            report.append(f"- **账户余额**: {risk_mgmt['account_balance']:.2f}")
            report.append(f"- **风险金额**: {risk_mgmt['risk_amount']:.2f} ({risk_mgmt['risk_percentage']:.2f}%)")
            report.append(f"- **风险控制最大仓位**: {risk_mgmt['max_risk_position']}手")
            report.append("")

            # 写入报告文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report))

            logger.info(f"成功生成分析报告: {output_file}")

            # 生成图表
            if include_charts:
                self._generate_charts(output_dir)

            return output_file
        except Exception as e:
            logger.error(f"生成分析报告出错: {e}")
            return False

    def _generate_charts(self, output_dir):
        """生成技术分析图表"""
        try:
            # 准备数据
            df = self.data.copy()

            # 转换为mplfinance可用的格式
            df['Date'] = pd.to_datetime(df['交易日期'], format='%Y%m%d')
            df.set_index('Date', inplace=True)
            df_mpf = df[['open', 'high', 'low', 'close', 'volume']].copy()

            # 设置图表样式
            mc = mpf.make_marketcolors(
                up='red', down='green',
                edge='inherit',
                wick='inherit',
                volume='inherit'
            )
            s = mpf.make_mpf_style(
                marketcolors=mc,
                gridstyle='-',
                y_on_right=False
            )

            # 添加均线
            ma_periods = self.config.get("technical_indicators", {}).get("ma_periods", [5, 10, 20, 60])
            ma_values = []
            for period in ma_periods:
                if f'MA{period}' in df.columns:
                    ma_values.append(mpf.make_addplot(df[f'MA{period}'], width=0.7, label=f'MA{period}'))

            # 添加MACD
            if 'MACD' in df.columns and 'MACD_SIGNAL' in df.columns and 'MACD_HIST' in df.columns:
                macd_plot = mpf.make_addplot(df['MACD'], panel=1, color='blue', width=0.7, label='MACD')
                signal_plot = mpf.make_addplot(df['MACD_SIGNAL'], panel=1, color='red', width=0.7, label='Signal')
                hist_plot = mpf.make_addplot(df['MACD_HIST'], panel=1, type='bar', color='dimgray', label='Histogram')
                ma_values.extend([macd_plot, signal_plot, hist_plot])

            # 添加RSI
            if 'RSI' in df.columns:
                rsi_plot = mpf.make_addplot(df['RSI'], panel=2, color='purple', width=0.7, label='RSI')
                ma_values.append(rsi_plot)

            # 生成K线图
            date_str = self.analysis_results["date"]
            chart_file = os.path.join(output_dir, f"chart_{date_str}.png")

            # 设置图表标题和大小
            title = f'多晶硅期货技术分析 ({date_str})'
            fig, axes = mpf.plot(
                df_mpf.tail(60),  # 显示最近60个交易日
                type='candle',
                style=s,
                addplot=ma_values,
                volume=True,
                figsize=(12, 10),
                panel_ratios=(6, 2, 2),
                title=title,
                returnfig=True
            )

            # 添加图例
            axes[0].legend(loc='upper left')
            axes[2].legend(loc='upper left')
            axes[4].legend(loc='upper left')

            # 保存图表
            fig.savefig(chart_file)
            plt.close(fig)

            logger.info(f"成功生成技术分析图表: {chart_file}")
            return True
        except Exception as e:
            logger.error(f"生成技术分析图表出错: {e}")
            return False

    def run_daily_analysis(self, current_position=None, output_file=None, include_charts=True):
        """运行每日分析流程"""
        try:
            # 1. 加载数据
            if not self.load_data():
                return False

            # 2. 计算技术指标
            if not self.calculate_indicators():
                return False

            # 3. 分析市场状态
            if not self.analyze_market():
                return False

            # 4. 生成交易计划
            if not self.generate_trade_plan(current_position):
                return False

            # 5. 生成分析报告
            report_file = self.generate_report(output_file, include_charts)
            if not report_file:
                return False

            logger.info(f"每日分析流程完成，报告已保存至: {report_file}")
            return report_file
        except Exception as e:
            logger.error(f"运行每日分析流程出错: {e}")
            return False

if __name__ == "__main__":
    # 创建市场分析器
    analyzer = MarketAnalyzer()

    # 运行每日分析
    current_position = {
        "direction": "SHORT",
        "size": 5,
        "entry_price": 37000
    }

    report_file = analyzer.run_daily_analysis(current_position)

    if report_file:
        print(f"分析报告已生成: {report_file}")
    else:
        print("分析失败，请查看日志了解详情")
