{"check_interval": 60, "trading_hours": [{"start": "09:00:00", "end": "11:30:00"}, {"start": "13:30:00", "end": "15:00:00"}], "data_save_path": "price_data.csv", "historical_data_file": "PSFUTURES2025.csv", "contracts": [{"symbol": "ps2506", "exchange": "GFEX", "name": "多晶硅主力合约"}], "strategies": {"atr_strategy": {"enabled": true, "atr_period": 14, "atr_multiplier": 2.5, "entry_rules": {"short": {"price_condition": "price > previous_high", "atr_condition": "price > (ma20 + (atr * 0.5))"}, "long": {"price_condition": "price < previous_low", "atr_condition": "price < (ma20 - (atr * 0.5))"}}, "exit_rules": {"short": {"take_profit": [{"percent": 0.3, "target": "(entry_price - (atr * 1.5))"}, {"percent": 0.3, "target": "(entry_price - (atr * 2.5))"}, {"percent": 0.2, "target": "(entry_price - (atr * 4.0))"}], "stop_loss": "(entry_price + (atr * 2.5))"}, "long": {"take_profit": [{"percent": 0.3, "target": "entry_price + atr * 1.5"}, {"percent": 0.3, "target": "entry_price + atr * 2.5"}, {"percent": 0.2, "target": "entry_price + atr * 4.0"}], "stop_loss": "entry_price - atr * 2.5"}}}}}