import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import time
import logging

class AKShareDataSource:
    """AKShare数据源"""

    def __init__(self):
        self.logger = logging.getLogger("AKShareDataSource")
        self.cache = {}
        self.cache_time = {}
        self.cache_duration = 60  # 缓存时间，单位秒

    def get_realtime_data(self, symbol, exchange=None):
        """获取实时行情数据"""
        current_time = time.time()
        cache_key = symbol

        # 检查缓存是否有效
        if (cache_key in self.cache and
            current_time - self.cache_time.get(cache_key, 0) < self.cache_duration):
            return self.cache[cache_key]

        try:
            self.logger.info(f"获取{symbol}实时行情数据")

            # 获取期货实时行情
            try:
                # 尝试使用futures_zh_spot
                if symbol.startswith('ps'):  # 多晶硅期货
                    df = ak.futures_zh_spot(symbol=symbol, market="GFEX")
                else:
                    # 其他期货品种
                    df = ak.futures_zh_spot(symbol=symbol)
            except (AttributeError, Exception) as e:
                self.logger.warning(f"futures_zh_spot失败: {e}，尝试其他方法")
                try:
                    # 尝试使用futures_zh_realtime
                    df = ak.futures_zh_realtime(symbol=symbol)
                except (AttributeError, Exception) as e2:
                    self.logger.warning(f"futures_zh_realtime失败: {e2}，尝试其他方法")
                    # 尝试使用期货实时行情API
                    df = ak.futures_zh_minute_sina(symbol=symbol, period="1")

            if df is not None and not df.empty:
                # 打印原始数据，用于调试
                self.logger.debug(f"原始数据: \n{df.head()}")

                # 处理数据
                result = {
                    'symbol': symbol,
                    'last_price': float(df['最新价'].iloc[0]) if '最新价' in df.columns else None,
                    'open': float(df['开盘价'].iloc[0]) if '开盘价' in df.columns else None,
                    'high': float(df['最高价'].iloc[0]) if '最高价' in df.columns else None,
                    'low': float(df['最低价'].iloc[0]) if '最低价' in df.columns else None,
                    'volume': float(df['成交量'].iloc[0]) if '成交量' in df.columns else None,
                    'amount': float(df['成交额'].iloc[0]) if '成交额' in df.columns else None,
                    'bid_price': float(df['买价'].iloc[0]) if '买价' in df.columns else None,
                    'ask_price': float(df['卖价'].iloc[0]) if '卖价' in df.columns else None,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                }

                # 更新缓存
                self.cache[cache_key] = result
                self.cache_time[cache_key] = current_time

                self.logger.info(f"成功获取{symbol}实时数据")
                return result
            else:
                self.logger.warning(f"获取{symbol}实时数据失败，返回空数据")
                return None
        except Exception as e:
            self.logger.error(f"获取{symbol}实时数据出错: {e}")
            return None

    def get_historical_data(self, symbol, period='daily', start_date=None, end_date=None):
        """获取历史数据"""
        try:
            self.logger.info(f"获取{symbol}历史数据，周期:{period}")

            if start_date is None:
                start_date = (datetime.now() - timedelta(days=60)).strftime('%Y%m%d')
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')

            # 获取期货历史数据
            if period == 'daily':
                if symbol.startswith('ps'):  # 多晶硅期货
                    df = ak.futures_zh_daily(symbol=symbol)
                else:
                    df = ak.futures_zh_daily(symbol=symbol)
            elif period == 'minute':
                df = ak.futures_zh_minute_sina(symbol=symbol)
            else:
                raise ValueError(f"不支持的周期: {period}")

            if df is not None and not df.empty:
                self.logger.info(f"成功获取{symbol}历史数据，共{len(df)}条记录")
                return df
            else:
                self.logger.warning(f"获取{symbol}历史数据失败，返回空数据")
                return None
        except Exception as e:
            self.logger.error(f"获取{symbol}历史数据出错: {e}")
            return None

    def calculate_indicators(self, symbol, indicators=None):
        """计算技术指标"""
        if indicators is None:
            indicators = ['atr', 'ma20', 'volume_ma5']

        try:
            self.logger.info(f"计算{symbol}技术指标: {indicators}")

            # 获取历史数据
            df = self.get_historical_data(symbol)
            if df is None or df.empty:
                self.logger.warning(f"无法计算{symbol}技术指标，历史数据为空")
                return None

            # 确保列名一致性
            if '收盘价' not in df.columns and 'close' in df.columns:
                df['收盘价'] = df['close']
            if '最高价' not in df.columns and 'high' in df.columns:
                df['最高价'] = df['high']
            if '最低价' not in df.columns and 'low' in df.columns:
                df['最低价'] = df['low']
            if '成交量' not in df.columns and 'volume' in df.columns:
                df['成交量'] = df['volume']

            result = {}

            # 计算ATR
            if 'atr' in indicators:
                high = df['最高价'].astype(float)
                low = df['最低价'].astype(float)
                close = df['收盘价'].astype(float)

                tr1 = high - low
                tr2 = abs(high - close.shift(1))
                tr3 = abs(low - close.shift(1))
                tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
                atr = tr.rolling(window=14).mean()

                result['atr'] = atr.iloc[-1]
                result['atr_percentile'] = self._calculate_percentile(atr, atr.iloc[-1])

            # 计算MA20
            if 'ma20' in indicators:
                ma20 = df['收盘价'].astype(float).rolling(window=20).mean()
                result['ma20'] = ma20.iloc[-1]

            # 计算成交量5日均线
            if 'volume_ma5' in indicators:
                volume_ma5 = df['成交量'].astype(float).rolling(window=5).mean()
                result['volume_ma5'] = volume_ma5.iloc[-1]

            # 添加前一日数据
            if len(df) >= 2:
                result['previous_close'] = df['收盘价'].astype(float).iloc[-2]
                result['previous_high'] = df['最高价'].astype(float).iloc[-2]
                result['previous_low'] = df['最低价'].astype(float).iloc[-2]

            self.logger.info(f"成功计算{symbol}技术指标")
            return result
        except Exception as e:
            self.logger.error(f"计算{symbol}技术指标出错: {e}")
            return None

    def _calculate_percentile(self, series, value):
        """计算数值在序列中的百分位"""
        return sum(series < value) / len(series) * 100
