"""
市场分析模块
用于分析日K线数据，识别市场状态和趋势，生成每日交易计划
"""

import os
import pandas as pd
import numpy as np
import talib
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import mplfinance as mpf
import json

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('market_analysis.log')
    ]
)
logger = logging.getLogger("MarketAnalysis")

class MarketAnalyzer:
    """市场分析器"""
    
    def __init__(self, config_file="market_analysis_config.json"):
        """初始化"""
        self.config = self._load_config(config_file)
        self.data = None
        self.analysis_results = {}
        self.trade_plan = {}
        
        # 加载中文字体
        try:
            self.font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf")
        except:
            self.font = None
            logger.warning("无法加载中文字体，图表中的中文可能显示为乱码")
        
        logger.info("市场分析器初始化完成")
    
    def _load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "data_file": "PSFUTURES2025.csv",  # 历史数据文件
            "output_dir": "analysis_reports",   # 输出目录
            "technical_indicators": {
                "ma_periods": [5, 10, 20, 60],  # 移动平均线周期
                "rsi_period": 14,               # RSI周期
                "macd_params": [12, 26, 9],     # MACD参数
                "boll_period": 20,              # 布林带周期
                "atr_period": 14,               # ATR周期
                "kd_params": [9, 3, 3],         # KDJ参数
            },
            "overbought_oversold": {
                "rsi_overbought": 70,           # RSI超买阈值
                "rsi_oversold": 30,             # RSI超卖阈值
                "kd_overbought": 80,            # KD超买阈值
                "kd_oversold": 20,              # KD超卖阈值
            },
            "trend_thresholds": {
                "strong_uptrend": 0.05,         # 强上升趋势阈值
                "uptrend": 0.02,                # 上升趋势阈值
                "downtrend": -0.02,             # 下降趋势阈值
                "strong_downtrend": -0.05,      # 强下降趋势阈值
            },
            "fundamental_data": {
                "supply": 1000000,              # 供应量（吨）
                "demand": 800000,               # 需求量（吨）
                "inventory": 400000,            # 库存量（吨）
                "production_cost": 30000,       # 生产成本（元/吨）
            },
            "position_management": {
                "max_position": 10,             # 最大持仓（手）
                "risk_per_trade": 2.0,          # 单笔风险（%）
                "stop_loss_atr_multiple": 2.5,  # 止损ATR倍数
                "take_profit_levels": [         # 止盈级别
                    {"percent": 0.3, "atr_multiple": 1.5},
                    {"percent": 0.3, "atr_multiple": 2.5},
                    {"percent": 0.2, "atr_multiple": 4.0}
                ]
            }
        }
        
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"成功加载配置文件: {config_file}")
                return config
            else:
                logger.warning(f"配置文件不存在: {config_file}，使用默认配置")
                return default_config
        except Exception as e:
            logger.error(f"加载配置文件出错: {e}，使用默认配置")
            return default_config
    
    def load_data(self, data_file=None):
        """加载历史数据"""
        try:
            file_path = data_file or self.config.get("data_file")
            if not file_path:
                logger.error("未指定数据文件")
                return False
            
            if not os.path.exists(file_path):
                logger.error(f"数据文件不存在: {file_path}")
                return False
            
            # 读取CSV文件
            self.data = pd.read_csv(file_path, encoding='utf-8', skiprows=1)
            
            # 确保数值列为浮点型
            numeric_columns = ['开盘价', '最高价', '最低价', '收盘价', '前结算价', '结算价', '涨跌', '涨跌1', '成交量', '持仓量']
            for col in numeric_columns:
                if col in self.data.columns:
                    self.data[col] = pd.to_numeric(self.data[col], errors='coerce')
            
            # 按日期排序
            self.data = self.data.sort_values(by='交易日期')
            
            # 重命名列以便使用talib
            if '收盘价' in self.data.columns:
                self.data['close'] = self.data['收盘价']
            if '开盘价' in self.data.columns:
                self.data['open'] = self.data['开盘价']
            if '最高价' in self.data.columns:
                self.data['high'] = self.data['最高价']
            if '最低价' in self.data.columns:
                self.data['low'] = self.data['最低价']
            if '成交量' in self.data.columns:
                self.data['volume'] = self.data['成交量']
            
            logger.info(f"成功加载数据，共{len(self.data)}条记录")
            return True
        except Exception as e:
            logger.error(f"加载数据出错: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标"""
        if self.data is None or self.data.empty:
            logger.error("数据为空，无法计算指标")
            return False
        
        try:
            # 获取配置
            tech_config = self.config.get("technical_indicators", {})
            
            # 计算移动平均线
            ma_periods = tech_config.get("ma_periods", [5, 10, 20, 60])
            for period in ma_periods:
                self.data[f'MA{period}'] = talib.SMA(self.data['close'], timeperiod=period)
            
            # 计算RSI
            rsi_period = tech_config.get("rsi_period", 14)
            self.data['RSI'] = talib.RSI(self.data['close'], timeperiod=rsi_period)
            
            # 计算MACD
            macd_params = tech_config.get("macd_params", [12, 26, 9])
            macd, macd_signal, macd_hist = talib.MACD(
                self.data['close'], 
                fastperiod=macd_params[0], 
                slowperiod=macd_params[1], 
                signalperiod=macd_params[2]
            )
            self.data['MACD'] = macd
            self.data['MACD_SIGNAL'] = macd_signal
            self.data['MACD_HIST'] = macd_hist
            
            # 计算布林带
            boll_period = tech_config.get("boll_period", 20)
            upper, middle, lower = talib.BBANDS(
                self.data['close'], 
                timeperiod=boll_period, 
                nbdevup=2, 
                nbdevdn=2, 
                matype=0
            )
            self.data['BOLL_UPPER'] = upper
            self.data['BOLL_MIDDLE'] = middle
            self.data['BOLL_LOWER'] = lower
            
            # 计算ATR
            atr_period = tech_config.get("atr_period", 14)
            self.data['ATR'] = talib.ATR(
                self.data['high'], 
                self.data['low'], 
                self.data['close'], 
                timeperiod=atr_period
            )
            
            # 计算KDJ
            kd_params = tech_config.get("kd_params", [9, 3, 3])
            self.data['K'], self.data['D'] = talib.STOCH(
                self.data['high'], 
                self.data['low'], 
                self.data['close'], 
                fastk_period=kd_params[0], 
                slowk_period=kd_params[1], 
                slowk_matype=0, 
                slowd_period=kd_params[2], 
                slowd_matype=0
            )
            self.data['J'] = 3 * self.data['K'] - 2 * self.data['D']
            
            # 计算DMI
            self.data['PLUS_DI'] = talib.PLUS_DI(self.data['high'], self.data['low'], self.data['close'], timeperiod=14)
            self.data['MINUS_DI'] = talib.MINUS_DI(self.data['high'], self.data['low'], self.data['close'], timeperiod=14)
            self.data['ADX'] = talib.ADX(self.data['high'], self.data['low'], self.data['close'], timeperiod=14)
            
            # 计算CCI
            self.data['CCI'] = talib.CCI(self.data['high'], self.data['low'], self.data['close'], timeperiod=14)
            
            # 计算OBV
            self.data['OBV'] = talib.OBV(self.data['close'], self.data['volume'])
            
            logger.info("成功计算技术指标")
            return True
        except Exception as e:
            logger.error(f"计算技术指标出错: {e}")
            return False
    
    def analyze_market(self):
        """分析市场状态"""
        if self.data is None or self.data.empty:
            logger.error("数据为空，无法分析市场")
            return False
        
        try:
            # 获取最新数据
            latest_data = self.data.iloc[-1]
            prev_data = self.data.iloc[-2]
            
            # 获取配置
            overbought_oversold = self.config.get("overbought_oversold", {})
            trend_thresholds = self.config.get("trend_thresholds", {})
            
            # 初始化分析结果
            self.analysis_results = {
                "date": latest_data.get('交易日期', datetime.now().strftime('%Y%m%d')),
                "price": {
                    "current": latest_data['close'],
                    "open": latest_data['open'],
                    "high": latest_data['high'],
                    "low": latest_data['low'],
                    "prev_close": prev_data['close'],
                    "change": latest_data['close'] - prev_data['close'],
                    "change_percent": (latest_data['close'] - prev_data['close']) / prev_data['close'] * 100
                },
                "volume": {
                    "current": latest_data['volume'],
                    "prev": prev_data['volume'],
                    "change_percent": (latest_data['volume'] - prev_data['volume']) / prev_data['volume'] * 100 if prev_data['volume'] > 0 else 0
                },
                "indicators": {
                    "ma": {f"ma{period}": latest_data.get(f'MA{period}', None) for period in self.config.get("technical_indicators", {}).get("ma_periods", [5, 10, 20, 60])},
                    "rsi": latest_data['RSI'],
                    "macd": {
                        "macd": latest_data['MACD'],
                        "signal": latest_data['MACD_SIGNAL'],
                        "hist": latest_data['MACD_HIST']
                    },
                    "boll": {
                        "upper": latest_data['BOLL_UPPER'],
                        "middle": latest_data['BOLL_MIDDLE'],
                        "lower": latest_data['BOLL_LOWER']
                    },
                    "atr": latest_data['ATR'],
                    "kdj": {
                        "k": latest_data['K'],
                        "d": latest_data['D'],
                        "j": latest_data['J']
                    },
                    "dmi": {
                        "plus_di": latest_data['PLUS_DI'],
                        "minus_di": latest_data['MINUS_DI'],
                        "adx": latest_data['ADX']
                    },
                    "cci": latest_data['CCI'],
                    "obv": latest_data['OBV']
                },
                "market_status": {},
                "trend": {},
                "signals": {},
                "support_resistance": {}
            }
            
            # 分析市场状态
            # 1. 超买/超卖状态
            rsi = latest_data['RSI']
            k = latest_data['K']
            d = latest_data['D']
            
            if rsi > overbought_oversold.get("rsi_overbought", 70):
                self.analysis_results["market_status"]["rsi"] = "超买"
            elif rsi < overbought_oversold.get("rsi_oversold", 30):
                self.analysis_results["market_status"]["rsi"] = "超卖"
            else:
                self.analysis_results["market_status"]["rsi"] = "中性"
            
            if k > overbought_oversold.get("kd_overbought", 80) and d > overbought_oversold.get("kd_overbought", 80):
                self.analysis_results["market_status"]["kdj"] = "超买"
            elif k < overbought_oversold.get("kd_oversold", 20) and d < overbought_oversold.get("kd_oversold", 20):
                self.analysis_results["market_status"]["kdj"] = "超卖"
            else:
                self.analysis_results["market_status"]["kdj"] = "中性"
            
            # 2. 趋势分析
            # 短期趋势（5日均线与10日均线的关系）
            ma5 = latest_data.get('MA5')
            ma10 = latest_data.get('MA10')
            ma20 = latest_data.get('MA20')
            ma60 = latest_data.get('MA60')
            
            if ma5 is not None and ma10 is not None:
                if ma5 > ma10:
                    self.analysis_results["trend"]["short_term"] = "上升"
                else:
                    self.analysis_results["trend"]["short_term"] = "下降"
            
            # 中期趋势（10日均线与20日均线的关系）
            if ma10 is not None and ma20 is not None:
                if ma10 > ma20:
                    self.analysis_results["trend"]["medium_term"] = "上升"
                else:
                    self.analysis_results["trend"]["medium_term"] = "下降"
            
            # 长期趋势（20日均线与60日均线的关系）
            if ma20 is not None and ma60 is not None:
                if ma20 > ma60:
                    self.analysis_results["trend"]["long_term"] = "上升"
                else:
                    self.analysis_results["trend"]["long_term"] = "下降"
            
            # 3. 信号分析
            # MACD信号
            macd = latest_data['MACD']
            macd_signal = latest_data['MACD_SIGNAL']
            macd_hist = latest_data['MACD_HIST']
            prev_macd_hist = self.data.iloc[-2]['MACD_HIST']
            
            if macd > macd_signal and prev_macd_hist < 0 and macd_hist > 0:
                self.analysis_results["signals"]["macd"] = "金叉（买入）"
            elif macd < macd_signal and prev_macd_hist > 0 and macd_hist < 0:
                self.analysis_results["signals"]["macd"] = "死叉（卖出）"
            elif macd_hist > 0:
                self.analysis_results["signals"]["macd"] = "多头"
            else:
                self.analysis_results["signals"]["macd"] = "空头"
            
            # KDJ信号
            k = latest_data['K']
            d = latest_data['D']
            prev_k = self.data.iloc[-2]['K']
            prev_d = self.data.iloc[-2]['D']
            
            if k > d and prev_k <= prev_d:
                self.analysis_results["signals"]["kdj"] = "金叉（买入）"
            elif k < d and prev_k >= prev_d:
                self.analysis_results["signals"]["kdj"] = "死叉（卖出）"
            elif k > d:
                self.analysis_results["signals"]["kdj"] = "多头"
            else:
                self.analysis_results["signals"]["kdj"] = "空头"
            
            # DMI信号
            plus_di = latest_data['PLUS_DI']
            minus_di = latest_data['MINUS_DI']
            adx = latest_data['ADX']
            prev_plus_di = self.data.iloc[-2]['PLUS_DI']
            prev_minus_di = self.data.iloc[-2]['MINUS_DI']
            
            if plus_di > minus_di and prev_plus_di <= prev_minus_di:
                self.analysis_results["signals"]["dmi"] = "金叉（买入）"
            elif plus_di < minus_di and prev_plus_di >= prev_minus_di:
                self.analysis_results["signals"]["dmi"] = "死叉（卖出）"
            elif plus_di > minus_di:
                self.analysis_results["signals"]["dmi"] = "多头"
                if adx > 25:
                    self.analysis_results["signals"]["dmi"] += "（强）"
            else:
                self.analysis_results["signals"]["dmi"] = "空头"
                if adx > 25:
                    self.analysis_results["signals"]["dmi"] += "（强）"
            
            # 4. 支撑位和阻力位分析
            # 使用布林带作为支撑位和阻力位
            self.analysis_results["support_resistance"]["boll_upper"] = latest_data['BOLL_UPPER']
            self.analysis_results["support_resistance"]["boll_middle"] = latest_data['BOLL_MIDDLE']
            self.analysis_results["support_resistance"]["boll_lower"] = latest_data['BOLL_LOWER']
            
            # 使用前期高点和低点作为支撑位和阻力位
            recent_data = self.data.iloc[-20:]  # 最近20个交易日
            recent_highs = recent_data['high'].nlargest(3)
            recent_lows = recent_data['low'].nsmallest(3)
            
            self.analysis_results["support_resistance"]["recent_highs"] = recent_highs.tolist()
            self.analysis_results["support_resistance"]["recent_lows"] = recent_lows.tolist()
            
            # 5. 综合评估
            # 根据各项指标的信号，给出综合评估
            bullish_signals = 0
            bearish_signals = 0
            
            # 计算看多信号数量
            if self.analysis_results["market_status"]["rsi"] == "超卖":
                bullish_signals += 1
            if self.analysis_results["market_status"]["kdj"] == "超卖":
                bullish_signals += 1
            if self.analysis_results["signals"]["macd"] in ["金叉（买入）", "多头"]:
                bullish_signals += 1
            if self.analysis_results["signals"]["kdj"] in ["金叉（买入）", "多头"]:
                bullish_signals += 1
            if self.analysis_results["signals"]["dmi"] in ["金叉（买入）", "多头", "多头（强）"]:
                bullish_signals += 1
            if self.analysis_results["trend"]["short_term"] == "上升":
                bullish_signals += 1
            
            # 计算看空信号数量
            if self.analysis_results["market_status"]["rsi"] == "超买":
                bearish_signals += 1
            if self.analysis_results["market_status"]["kdj"] == "超买":
                bearish_signals += 1
            if self.analysis_results["signals"]["macd"] in ["死叉（卖出）", "空头"]:
                bearish_signals += 1
            if self.analysis_results["signals"]["kdj"] in ["死叉（卖出）", "空头"]:
                bearish_signals += 1
            if self.analysis_results["signals"]["dmi"] in ["死叉（卖出）", "空头", "空头（强）"]:
                bearish_signals += 1
            if self.analysis_results["trend"]["short_term"] == "下降":
                bearish_signals += 1
            
            # 综合评估
            self.analysis_results["overall_assessment"] = {
                "bullish_signals": bullish_signals,
                "bearish_signals": bearish_signals,
                "neutral_signals": 6 - bullish_signals - bearish_signals,  # 假设总共有6个信号
                "conclusion": ""
            }
            
            if bullish_signals > bearish_signals + 2:
                self.analysis_results["overall_assessment"]["conclusion"] = "强烈看多"
            elif bullish_signals > bearish_signals:
                self.analysis_results["overall_assessment"]["conclusion"] = "偏多"
            elif bearish_signals > bullish_signals + 2:
                self.analysis_results["overall_assessment"]["conclusion"] = "强烈看空"
            elif bearish_signals > bullish_signals:
                self.analysis_results["overall_assessment"]["conclusion"] = "偏空"
            else:
                self.analysis_results["overall_assessment"]["conclusion"] = "中性"
            
            logger.info("成功分析市场状态")
            return True
        except Exception as e:
            logger.error(f"分析市场状态出错: {e}")
            return False
