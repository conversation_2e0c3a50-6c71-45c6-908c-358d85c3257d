"""
综合市场分析模块
结合技术面、基本面、情绪面和资金面进行全面分析
"""

import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import mplfinance as mpf
import json
import requests
from bs4 import BeautifulSoup
import re

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('comprehensive_analysis.log')
    ]
)
logger = logging.getLogger("ComprehensiveAnalysis")

class ComprehensiveMarketAnalyzer:
    """综合市场分析器"""

    def __init__(self, config_file="comprehensive_analysis_config.json"):
        """初始化"""
        self.config = self._load_config(config_file)
        self.data = None
        self.analysis_results = {}
        self.trade_plan = {}
        self.fundamental_data = {}
        self.sentiment_data = {}
        self.capital_flow_data = {}

        # 加载中文字体
        try:
            self.font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf")
        except:
            self.font = None
            logger.warning("无法加载中文字体，图表中的中文可能显示为乱码")

        logger.info("综合市场分析器初始化完成")

    def _load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "data_file": "PSFUTURES2025.csv",  # 历史数据文件
            "output_dir": "analysis_reports",   # 输出目录
            "technical_indicators": {
                "ma_periods": [5, 10, 20, 60],  # 移动平均线周期
                "rsi_period": 14,               # RSI周期
                "macd_params": [12, 26, 9],     # MACD参数
                "boll_period": 20,              # 布林带周期
                "atr_period": 14,               # ATR周期
                "kd_params": [9, 3, 3],         # KDJ参数
            },
            "overbought_oversold": {
                "rsi_overbought": 70,           # RSI超买阈值
                "rsi_oversold": 30,             # RSI超卖阈值
                "kd_overbought": 80,            # KD超买阈值
                "kd_oversold": 20,              # KD超卖阈值
            },
            "trend_thresholds": {
                "strong_uptrend": 0.05,         # 强上升趋势阈值
                "uptrend": 0.02,                # 上升趋势阈值
                "downtrend": -0.02,             # 下降趋势阈值
                "strong_downtrend": -0.05,      # 强下降趋势阈值
            },
            "fundamental_data": {
                "supply": {
                    "current": 1000000,         # 当前供应量（吨）
                    "previous": 980000,         # 上期供应量（吨）
                    "change": 20000,            # 变化量（吨）
                    "change_percent": 2.04      # 变化百分比（%）
                },
                "demand": {
                    "current": 800000,          # 当前需求量（吨）
                    "previous": 820000,         # 上期需求量（吨）
                    "change": -20000,           # 变化量（吨）
                    "change_percent": -2.44     # 变化百分比（%）
                },
                "inventory": {
                    "current": 400000,          # 当前库存量（吨）
                    "previous": 380000,         # 上期库存量（吨）
                    "change": 20000,            # 变化量（吨）
                    "change_percent": 5.26      # 变化百分比（%）
                },
                "production_cost": 30000,       # 生产成本（元/吨）
                "news": [
                    {
                        "date": "2025-05-08",
                        "title": "多晶硅产能持续扩张，供应过剩压力加大",
                        "impact": "bearish"     # 利空
                    },
                    {
                        "date": "2025-05-07",
                        "title": "光伏装机需求不及预期，多晶硅价格承压",
                        "impact": "bearish"     # 利空
                    },
                    {
                        "date": "2025-05-05",
                        "title": "部分多晶硅企业减产检修，短期供应收紧",
                        "impact": "bullish"     # 利多
                    }
                ]
            },
            "sentiment_data": {
                "market_sentiment": "bearish",  # 市场情绪：看空
                "sentiment_indicators": {
                    "put_call_ratio": 1.5,      # 看跌/看涨期权比率
                    "long_short_ratio": 0.8,    # 多/空持仓比率
                    "retail_sentiment": "bearish", # 散户情绪
                    "institutional_sentiment": "bearish" # 机构情绪
                },
                "social_media_sentiment": {
                    "positive": 30,             # 正面情绪占比（%）
                    "neutral": 20,              # 中性情绪占比（%）
                    "negative": 50              # 负面情绪占比（%）
                }
            },
            "capital_flow_data": {
                "net_flow": -50000000,          # 净资金流入（元）
                "institutional_flow": -30000000, # 机构资金流入（元）
                "retail_flow": -20000000,       # 散户资金流入（元）
                "foreign_flow": -5000000,       # 外资资金流入（元）
                "sector_rotation": "out",       # 板块资金流向：流出
                "concentration_ratio": 0.6      # 资金集中度
            },
            "position_management": {
                "max_position": 10,             # 最大持仓（手）
                "risk_per_trade": 2.0,          # 单笔风险（%）
                "stop_loss_atr_multiple": 2.5,  # 止损ATR倍数
                "take_profit_levels": [         # 止盈级别
                    {"percent": 0.3, "atr_multiple": 1.5},
                    {"percent": 0.3, "atr_multiple": 2.5},
                    {"percent": 0.2, "atr_multiple": 4.0}
                ]
            }
        }

        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"成功加载配置文件: {config_file}")
                return config
            else:
                logger.warning(f"配置文件不存在: {config_file}，使用默认配置")
                return default_config
        except Exception as e:
            logger.error(f"加载配置文件出错: {e}，使用默认配置")
            return default_config

    def load_data(self, data_file=None):
        """加载历史数据"""
        try:
            file_path = data_file or self.config.get("data_file")
            if not file_path:
                logger.error("未指定数据文件")
                return False

            if not os.path.exists(file_path):
                logger.error(f"数据文件不存在: {file_path}")
                return False

            # 读取CSV文件
            self.data = pd.read_csv(file_path, encoding='utf-8', skiprows=1)

            # 确保数值列为浮点型
            numeric_columns = ['开盘价', '最高价', '最低价', '收盘价', '前结算价', '结算价', '涨跌', '涨跌1', '成交量', '持仓量']
            for col in numeric_columns:
                if col in self.data.columns:
                    self.data[col] = pd.to_numeric(self.data[col], errors='coerce')

            # 按日期排序
            self.data = self.data.sort_values(by='交易日期')

            # 重命名列以便使用
            if '收盘价' in self.data.columns:
                self.data['close'] = self.data['收盘价']
            if '开盘价' in self.data.columns:
                self.data['open'] = self.data['开盘价']
            if '最高价' in self.data.columns:
                self.data['high'] = self.data['最高价']
            if '最低价' in self.data.columns:
                self.data['low'] = self.data['最低价']
            if '成交量' in self.data.columns:
                self.data['volume'] = self.data['成交量']

            logger.info(f"成功加载数据，共{len(self.data)}条记录")
            return True
        except Exception as e:
            logger.error(f"加载数据出错: {e}")
            return False

    def calculate_indicators(self):
        """计算技术指标"""
        if self.data is None or self.data.empty:
            logger.error("数据为空，无法计算指标")
            return False

        try:
            # 获取配置
            tech_config = self.config.get("technical_indicators", {})

            # 计算移动平均线
            ma_periods = tech_config.get("ma_periods", [5, 10, 20, 60])
            for period in ma_periods:
                self.data[f'MA{period}'] = self.data['close'].rolling(window=period).mean()

            # 计算RSI
            rsi_period = tech_config.get("rsi_period", 14)
            delta = self.data['close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=rsi_period).mean()
            avg_loss = loss.rolling(window=rsi_period).mean()
            rs = avg_gain / avg_loss
            self.data['RSI'] = 100 - (100 / (1 + rs))

            # 计算MACD
            macd_params = tech_config.get("macd_params", [12, 26, 9])
            exp1 = self.data['close'].ewm(span=macd_params[0], adjust=False).mean()
            exp2 = self.data['close'].ewm(span=macd_params[1], adjust=False).mean()
            self.data['MACD'] = exp1 - exp2
            self.data['MACD_SIGNAL'] = self.data['MACD'].ewm(span=macd_params[2], adjust=False).mean()
            self.data['MACD_HIST'] = self.data['MACD'] - self.data['MACD_SIGNAL']

            # 计算布林带
            boll_period = tech_config.get("boll_period", 20)
            self.data['BOLL_MIDDLE'] = self.data['close'].rolling(window=boll_period).mean()
            self.data['BOLL_STD'] = self.data['close'].rolling(window=boll_period).std()
            self.data['BOLL_UPPER'] = self.data['BOLL_MIDDLE'] + 2 * self.data['BOLL_STD']
            self.data['BOLL_LOWER'] = self.data['BOLL_MIDDLE'] - 2 * self.data['BOLL_STD']

            # 计算ATR
            atr_period = tech_config.get("atr_period", 14)
            high_low = self.data['high'] - self.data['low']
            high_close = np.abs(self.data['high'] - self.data['close'].shift())
            low_close = np.abs(self.data['low'] - self.data['close'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = np.max(ranges, axis=1)
            self.data['ATR'] = true_range.rolling(window=atr_period).mean()

            # 计算KDJ
            kd_params = tech_config.get("kd_params", [9, 3, 3])
            low_min = self.data['low'].rolling(window=kd_params[0]).min()
            high_max = self.data['high'].rolling(window=kd_params[0]).max()
            rsv = 100 * ((self.data['close'] - low_min) / (high_max - low_min))
            self.data['K'] = rsv.rolling(window=kd_params[1]).mean()
            self.data['D'] = self.data['K'].rolling(window=kd_params[2]).mean()
            self.data['J'] = 3 * self.data['K'] - 2 * self.data['D']

            # 计算DMI
            # 简化版DMI计算
            up_move = self.data['high'].diff()
            down_move = self.data['low'].diff(-1).abs()
            plus_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
            minus_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)
            self.data['PLUS_DI'] = pd.Series(plus_dm).rolling(window=14).mean() / self.data['ATR'] * 100
            self.data['MINUS_DI'] = pd.Series(minus_dm).rolling(window=14).mean() / self.data['ATR'] * 100
            self.data['ADX'] = np.abs(self.data['PLUS_DI'] - self.data['MINUS_DI']) / (self.data['PLUS_DI'] + self.data['MINUS_DI']) * 100
            self.data['ADX'] = self.data['ADX'].rolling(window=14).mean()

            # 计算CCI
            tp = (self.data['high'] + self.data['low'] + self.data['close']) / 3
            tp_ma = tp.rolling(window=14).mean()
            md = (tp - tp_ma).abs().rolling(window=14).mean()
            self.data['CCI'] = (tp - tp_ma) / (0.015 * md)

            # 计算OBV
            obv = [0]
            for i in range(1, len(self.data)):
                if self.data['close'].iloc[i] > self.data['close'].iloc[i-1]:
                    obv.append(obv[-1] + self.data['volume'].iloc[i])
                elif self.data['close'].iloc[i] < self.data['close'].iloc[i-1]:
                    obv.append(obv[-1] - self.data['volume'].iloc[i])
                else:
                    obv.append(obv[-1])
            self.data['OBV'] = pd.Series(obv, index=self.data.index)

            # 计算波动率
            self.data['VOLATILITY'] = self.data['close'].pct_change().rolling(window=20).std() * np.sqrt(252) * 100

            # 计算价格动量
            self.data['MOMENTUM'] = self.data['close'].pct_change(periods=10) * 100

            # 计算价格与均线的偏离度
            for period in ma_periods:
                self.data[f'MA{period}_DEVIATION'] = (self.data['close'] - self.data[f'MA{period}']) / self.data[f'MA{period}'] * 100

            logger.info("成功计算技术指标")
            return True
        except Exception as e:
            logger.error(f"计算技术指标出错: {e}")
            return False

    def load_fundamental_data(self):
        """加载基本面数据"""
        try:
            # 从配置文件加载基本面数据
            self.fundamental_data = self.config.get("fundamental_data", {})

            # 这里可以添加从外部API或网站爬取最新基本面数据的代码
            # 例如：供需数据、库存数据、产能数据等

            logger.info("成功加载基本面数据")
            return True
        except Exception as e:
            logger.error(f"加载基本面数据出错: {e}")
            return False

    def load_sentiment_data(self):
        """加载情绪面数据"""
        try:
            # 从配置文件加载情绪面数据
            self.sentiment_data = self.config.get("sentiment_data", {})

            # 这里可以添加从外部API或网站爬取最新情绪面数据的代码
            # 例如：社交媒体情绪、期权市场情绪、投资者情绪调查等

            logger.info("成功加载情绪面数据")
            return True
        except Exception as e:
            logger.error(f"加载情绪面数据出错: {e}")
            return False

    def load_capital_flow_data(self):
        """加载资金面数据"""
        try:
            # 从配置文件加载资金面数据
            self.capital_flow_data = self.config.get("capital_flow_data", {})

            # 这里可以添加从外部API或网站爬取最新资金面数据的代码
            # 例如：资金流向、持仓变化、成交量变化等

            logger.info("成功加载资金面数据")
            return True
        except Exception as e:
            logger.error(f"加载资金面数据出错: {e}")
            return False

    def analyze_market(self):
        """分析市场状态"""
        if self.data is None or self.data.empty:
            logger.error("数据为空，无法分析市场")
            return False

        try:
            # 获取最新数据
            latest_data = self.data.iloc[-1]
            prev_data = self.data.iloc[-2]

            # 获取配置
            overbought_oversold = self.config.get("overbought_oversold", {})
            trend_thresholds = self.config.get("trend_thresholds", {})

            # 初始化分析结果
            self.analysis_results = {
                "date": latest_data.get('交易日期', datetime.now().strftime('%Y%m%d')),
                "price": {
                    "current": latest_data['close'],
                    "open": latest_data['open'],
                    "high": latest_data['high'],
                    "low": latest_data['low'],
                    "prev_close": prev_data['close'],
                    "change": latest_data['close'] - prev_data['close'],
                    "change_percent": (latest_data['close'] - prev_data['close']) / prev_data['close'] * 100
                },
                "volume": {
                    "current": latest_data['volume'],
                    "prev": prev_data['volume'],
                    "change_percent": (latest_data['volume'] - prev_data['volume']) / prev_data['volume'] * 100 if prev_data['volume'] > 0 else 0
                },
                "indicators": {
                    "ma": {f"ma{period}": latest_data.get(f'MA{period}', None) for period in self.config.get("technical_indicators", {}).get("ma_periods", [5, 10, 20, 60])},
                    "rsi": latest_data['RSI'],
                    "macd": {
                        "macd": latest_data['MACD'],
                        "signal": latest_data['MACD_SIGNAL'],
                        "hist": latest_data['MACD_HIST']
                    },
                    "boll": {
                        "upper": latest_data['BOLL_UPPER'],
                        "middle": latest_data['BOLL_MIDDLE'],
                        "lower": latest_data['BOLL_LOWER']
                    },
                    "atr": latest_data['ATR'],
                    "kdj": {
                        "k": latest_data['K'],
                        "d": latest_data['D'],
                        "j": latest_data['J']
                    },
                    "dmi": {
                        "plus_di": latest_data['PLUS_DI'],
                        "minus_di": latest_data['MINUS_DI'],
                        "adx": latest_data['ADX']
                    },
                    "cci": latest_data['CCI'],
                    "obv": latest_data['OBV'],
                    "volatility": latest_data['VOLATILITY'],
                    "momentum": latest_data['MOMENTUM'],
                    "ma_deviation": {f"ma{period}_deviation": latest_data.get(f'MA{period}_DEVIATION', None) for period in self.config.get("technical_indicators", {}).get("ma_periods", [5, 10, 20, 60])}
                },
                "market_status": {},
                "trend": {},
                "signals": {},
                "support_resistance": {},
                "fundamental": self.fundamental_data,
                "sentiment": self.sentiment_data,
                "capital_flow": self.capital_flow_data
            }

            # 分析市场状态
            # 1. 超买/超卖状态
            rsi = latest_data['RSI']
            k = latest_data['K']
            d = latest_data['D']

            if rsi > overbought_oversold.get("rsi_overbought", 70):
                self.analysis_results["market_status"]["rsi"] = "超买"
            elif rsi < overbought_oversold.get("rsi_oversold", 30):
                self.analysis_results["market_status"]["rsi"] = "超卖"
            else:
                self.analysis_results["market_status"]["rsi"] = "中性"

            if k > overbought_oversold.get("kd_overbought", 80) and d > overbought_oversold.get("kd_overbought", 80):
                self.analysis_results["market_status"]["kdj"] = "超买"
            elif k < overbought_oversold.get("kd_oversold", 20) and d < overbought_oversold.get("kd_oversold", 20):
                self.analysis_results["market_status"]["kdj"] = "超卖"
            else:
                self.analysis_results["market_status"]["kdj"] = "中性"

            # 2. 趋势分析
            # 短期趋势（5日均线与10日均线的关系）
            ma5 = latest_data.get('MA5')
            ma10 = latest_data.get('MA10')
            ma20 = latest_data.get('MA20')
            ma60 = latest_data.get('MA60')

            if ma5 is not None and ma10 is not None:
                if ma5 > ma10:
                    self.analysis_results["trend"]["short_term"] = "上升"
                else:
                    self.analysis_results["trend"]["short_term"] = "下降"

            # 中期趋势（10日均线与20日均线的关系）
            if ma10 is not None and ma20 is not None:
                if ma10 > ma20:
                    self.analysis_results["trend"]["medium_term"] = "上升"
                else:
                    self.analysis_results["trend"]["medium_term"] = "下降"

            # 长期趋势（20日均线与60日均线的关系）
            if ma20 is not None and ma60 is not None:
                if ma20 > ma60:
                    self.analysis_results["trend"]["long_term"] = "上升"
                else:
                    self.analysis_results["trend"]["long_term"] = "下降"

            # 3. 信号分析
            # MACD信号
            macd = latest_data['MACD']
            macd_signal = latest_data['MACD_SIGNAL']
            macd_hist = latest_data['MACD_HIST']
            prev_macd_hist = self.data.iloc[-2]['MACD_HIST']

            if macd > macd_signal and prev_macd_hist < 0 and macd_hist > 0:
                self.analysis_results["signals"]["macd"] = "金叉（买入）"
            elif macd < macd_signal and prev_macd_hist > 0 and macd_hist < 0:
                self.analysis_results["signals"]["macd"] = "死叉（卖出）"
            elif macd_hist > 0:
                self.analysis_results["signals"]["macd"] = "多头"
            else:
                self.analysis_results["signals"]["macd"] = "空头"

            # KDJ信号
            k = latest_data['K']
            d = latest_data['D']
            prev_k = self.data.iloc[-2]['K']
            prev_d = self.data.iloc[-2]['D']

            if k > d and prev_k <= prev_d:
                self.analysis_results["signals"]["kdj"] = "金叉（买入）"
            elif k < d and prev_k >= prev_d:
                self.analysis_results["signals"]["kdj"] = "死叉（卖出）"
            elif k > d:
                self.analysis_results["signals"]["kdj"] = "多头"
            else:
                self.analysis_results["signals"]["kdj"] = "空头"

            # DMI信号
            plus_di = latest_data['PLUS_DI']
            minus_di = latest_data['MINUS_DI']
            adx = latest_data['ADX']
            prev_plus_di = self.data.iloc[-2]['PLUS_DI']
            prev_minus_di = self.data.iloc[-2]['MINUS_DI']

            if plus_di > minus_di and prev_plus_di <= prev_minus_di:
                self.analysis_results["signals"]["dmi"] = "金叉（买入）"
            elif plus_di < minus_di and prev_plus_di >= prev_minus_di:
                self.analysis_results["signals"]["dmi"] = "死叉（卖出）"
            elif plus_di > minus_di:
                self.analysis_results["signals"]["dmi"] = "多头"
                if adx > 25:
                    self.analysis_results["signals"]["dmi"] += "（强）"
            else:
                self.analysis_results["signals"]["dmi"] = "空头"
                if adx > 25:
                    self.analysis_results["signals"]["dmi"] += "（强）"

            # 4. 支撑位和阻力位分析
            # 使用布林带作为支撑位和阻力位
            self.analysis_results["support_resistance"]["boll_upper"] = latest_data['BOLL_UPPER']
            self.analysis_results["support_resistance"]["boll_middle"] = latest_data['BOLL_MIDDLE']
            self.analysis_results["support_resistance"]["boll_lower"] = latest_data['BOLL_LOWER']

            # 使用前期高点和低点作为支撑位和阻力位
            recent_data = self.data.iloc[-14:]  # 最近两周（14个交易日）
            recent_highs = recent_data['high'].nlargest(3)
            recent_lows = recent_data['low'].nsmallest(3)

            self.analysis_results["support_resistance"]["recent_highs"] = recent_highs.tolist()
            self.analysis_results["support_resistance"]["recent_lows"] = recent_lows.tolist()

            # 5. 价格波动范围分析
            # 计算最近两周的价格波动范围
            price_range = recent_data['high'].max() - recent_data['low'].min()
            avg_atr = recent_data['ATR'].mean()

            self.analysis_results["price_range"] = {
                "high": recent_data['high'].max(),
                "low": recent_data['low'].min(),
                "range": price_range,
                "range_percent": price_range / latest_data['close'] * 100,
                "avg_atr": avg_atr,
                "range_atr_ratio": price_range / avg_atr if avg_atr > 0 else 0
            }

            # 6. 综合评估
            # 技术面评估
            bullish_signals = 0
            bearish_signals = 0

            # 计算看多信号数量
            if self.analysis_results["market_status"]["rsi"] == "超卖":
                bullish_signals += 1
            if self.analysis_results["market_status"]["kdj"] == "超卖":
                bullish_signals += 1
            if self.analysis_results["signals"]["macd"] in ["金叉（买入）", "多头"]:
                bullish_signals += 1
            if self.analysis_results["signals"]["kdj"] in ["金叉（买入）", "多头"]:
                bullish_signals += 1
            if self.analysis_results["signals"]["dmi"] in ["金叉（买入）", "多头", "多头（强）"]:
                bullish_signals += 1
            if self.analysis_results["trend"]["short_term"] == "上升":
                bullish_signals += 1

            # 计算看空信号数量
            if self.analysis_results["market_status"]["rsi"] == "超买":
                bearish_signals += 1
            if self.analysis_results["market_status"]["kdj"] == "超买":
                bearish_signals += 1
            if self.analysis_results["signals"]["macd"] in ["死叉（卖出）", "空头"]:
                bearish_signals += 1
            if self.analysis_results["signals"]["kdj"] in ["死叉（卖出）", "空头"]:
                bearish_signals += 1
            if self.analysis_results["signals"]["dmi"] in ["死叉（卖出）", "空头", "空头（强）"]:
                bearish_signals += 1
            if self.analysis_results["trend"]["short_term"] == "下降":
                bearish_signals += 1

            # 基本面评估
            fundamental_bullish = 0
            fundamental_bearish = 0

            # 供需关系
            supply = self.fundamental_data.get("supply", {})
            demand = self.fundamental_data.get("demand", {})
            inventory = self.fundamental_data.get("inventory", {})

            if supply.get("change_percent", 0) < 0:  # 供应减少
                fundamental_bullish += 1
            elif supply.get("change_percent", 0) > 0:  # 供应增加
                fundamental_bearish += 1

            if demand.get("change_percent", 0) > 0:  # 需求增加
                fundamental_bullish += 1
            elif demand.get("change_percent", 0) < 0:  # 需求减少
                fundamental_bearish += 1

            if inventory.get("change_percent", 0) < 0:  # 库存减少
                fundamental_bullish += 1
            elif inventory.get("change_percent", 0) > 0:  # 库存增加
                fundamental_bearish += 1

            # 新闻影响
            news = self.fundamental_data.get("news", [])
            for item in news:
                if item.get("impact") == "bullish":
                    fundamental_bullish += 0.5
                elif item.get("impact") == "bearish":
                    fundamental_bearish += 0.5

            # 情绪面评估
            sentiment_bullish = 0
            sentiment_bearish = 0

            market_sentiment = self.sentiment_data.get("market_sentiment", "neutral")
            if market_sentiment == "bullish":
                sentiment_bullish += 1
            elif market_sentiment == "bearish":
                sentiment_bearish += 1

            sentiment_indicators = self.sentiment_data.get("sentiment_indicators", {})

            if sentiment_indicators.get("put_call_ratio", 1.0) < 0.8:  # 看涨期权多
                sentiment_bullish += 1
            elif sentiment_indicators.get("put_call_ratio", 1.0) > 1.2:  # 看跌期权多
                sentiment_bearish += 1

            if sentiment_indicators.get("long_short_ratio", 1.0) > 1.2:  # 多头持仓多
                sentiment_bullish += 1
            elif sentiment_indicators.get("long_short_ratio", 1.0) < 0.8:  # 空头持仓多
                sentiment_bearish += 1

            # 资金面评估
            capital_bullish = 0
            capital_bearish = 0

            if self.capital_flow_data.get("net_flow", 0) > 0:  # 资金净流入
                capital_bullish += 1
            elif self.capital_flow_data.get("net_flow", 0) < 0:  # 资金净流出
                capital_bearish += 1

            if self.capital_flow_data.get("institutional_flow", 0) > 0:  # 机构资金流入
                capital_bullish += 1
            elif self.capital_flow_data.get("institutional_flow", 0) < 0:  # 机构资金流出
                capital_bearish += 1

            if self.capital_flow_data.get("sector_rotation", "") == "in":  # 板块资金流入
                capital_bullish += 1
            elif self.capital_flow_data.get("sector_rotation", "") == "out":  # 板块资金流出
                capital_bearish += 1

            # 综合评估
            total_bullish = bullish_signals + fundamental_bullish + sentiment_bullish + capital_bullish
            total_bearish = bearish_signals + fundamental_bearish + sentiment_bearish + capital_bearish

            self.analysis_results["overall_assessment"] = {
                "technical": {
                    "bullish_signals": bullish_signals,
                    "bearish_signals": bearish_signals,
                    "neutral_signals": 6 - bullish_signals - bearish_signals,  # 假设总共有6个信号
                    "conclusion": ""
                },
                "fundamental": {
                    "bullish_signals": fundamental_bullish,
                    "bearish_signals": fundamental_bearish,
                    "conclusion": ""
                },
                "sentiment": {
                    "bullish_signals": sentiment_bullish,
                    "bearish_signals": sentiment_bearish,
                    "conclusion": ""
                },
                "capital_flow": {
                    "bullish_signals": capital_bullish,
                    "bearish_signals": capital_bearish,
                    "conclusion": ""
                },
                "total": {
                    "bullish_signals": total_bullish,
                    "bearish_signals": total_bearish,
                    "conclusion": ""
                }
            }

            # 技术面结论
            if bullish_signals > bearish_signals + 2:
                self.analysis_results["overall_assessment"]["technical"]["conclusion"] = "强烈看多"
            elif bullish_signals > bearish_signals:
                self.analysis_results["overall_assessment"]["technical"]["conclusion"] = "偏多"
            elif bearish_signals > bullish_signals + 2:
                self.analysis_results["overall_assessment"]["technical"]["conclusion"] = "强烈看空"
            elif bearish_signals > bullish_signals:
                self.analysis_results["overall_assessment"]["technical"]["conclusion"] = "偏空"
            else:
                self.analysis_results["overall_assessment"]["technical"]["conclusion"] = "中性"

            # 基本面结论
            if fundamental_bullish > fundamental_bearish + 1:
                self.analysis_results["overall_assessment"]["fundamental"]["conclusion"] = "强烈看多"
            elif fundamental_bullish > fundamental_bearish:
                self.analysis_results["overall_assessment"]["fundamental"]["conclusion"] = "偏多"
            elif fundamental_bearish > fundamental_bullish + 1:
                self.analysis_results["overall_assessment"]["fundamental"]["conclusion"] = "强烈看空"
            elif fundamental_bearish > fundamental_bullish:
                self.analysis_results["overall_assessment"]["fundamental"]["conclusion"] = "偏空"
            else:
                self.analysis_results["overall_assessment"]["fundamental"]["conclusion"] = "中性"

            # 情绪面结论
            if sentiment_bullish > sentiment_bearish + 1:
                self.analysis_results["overall_assessment"]["sentiment"]["conclusion"] = "强烈看多"
            elif sentiment_bullish > sentiment_bearish:
                self.analysis_results["overall_assessment"]["sentiment"]["conclusion"] = "偏多"
            elif sentiment_bearish > sentiment_bullish + 1:
                self.analysis_results["overall_assessment"]["sentiment"]["conclusion"] = "强烈看空"
            elif sentiment_bearish > sentiment_bullish:
                self.analysis_results["overall_assessment"]["sentiment"]["conclusion"] = "偏空"
            else:
                self.analysis_results["overall_assessment"]["sentiment"]["conclusion"] = "中性"

            # 资金面结论
            if capital_bullish > capital_bearish + 1:
                self.analysis_results["overall_assessment"]["capital_flow"]["conclusion"] = "强烈看多"
            elif capital_bullish > capital_bearish:
                self.analysis_results["overall_assessment"]["capital_flow"]["conclusion"] = "偏多"
            elif capital_bearish > capital_bullish + 1:
                self.analysis_results["overall_assessment"]["capital_flow"]["conclusion"] = "强烈看空"
            elif capital_bearish > capital_bullish:
                self.analysis_results["overall_assessment"]["capital_flow"]["conclusion"] = "偏空"
            else:
                self.analysis_results["overall_assessment"]["capital_flow"]["conclusion"] = "中性"

            # 总体结论
            if total_bullish > total_bearish + 3:
                self.analysis_results["overall_assessment"]["total"]["conclusion"] = "强烈看多"
            elif total_bullish > total_bearish:
                self.analysis_results["overall_assessment"]["total"]["conclusion"] = "偏多"
            elif total_bearish > total_bullish + 3:
                self.analysis_results["overall_assessment"]["total"]["conclusion"] = "强烈看空"
            elif total_bearish > total_bullish:
                self.analysis_results["overall_assessment"]["total"]["conclusion"] = "偏空"
            else:
                self.analysis_results["overall_assessment"]["total"]["conclusion"] = "中性"

            logger.info("成功分析市场状态")
            return True
        except Exception as e:
            logger.error(f"分析市场状态出错: {e}")
            return False
