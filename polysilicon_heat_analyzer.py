import akshare as ak
import pandas as pd
import numpy as np
import requests
import json
import time
import random
import sqlite3
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import jieba
from snownlp import SnowNLP
import os
import re

class PolyiliconHeatAnalyzer:
    def __init__(self):
        self.db_path = "polysilicon_heat.db"
        self.conn = self._create_database()
        self.keywords = ["多晶硅", "多晶硅期货", "PS期货", "光伏硅料", "硅料价格"]
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self.report_dir = "reports"
        os.makedirs(self.report_dir, exist_ok=True)
    
    def _create_database(self):
        """创建SQLite数据库和表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建热度数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS heat_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT,
            source TEXT,
            keyword TEXT,
            value REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建新闻数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS news_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT,
            source TEXT,
            title TEXT,
            url TEXT,
            sentiment REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建社交媒体数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS social_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT,
            platform TEXT,
            content_type TEXT,
            count INTEGER,
            sentiment REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        # 创建综合热度表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS daily_heat_index (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT,
            total_heat REAL,
            search_heat REAL,
            social_heat REAL,
            news_heat REAL,
            forum_heat REAL,
            video_heat REAL,
            sentiment REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        return conn
    
    def collect_search_data(self):
        """收集搜索引擎数据"""
        print("正在收集搜索引擎数据...")
        
        # 模拟百度指数数据
        today = datetime.now().strftime("%Y-%m-%d")
        for keyword in self.keywords:
            # 模拟百度指数值 (基础值 + 随机波动)
            base_value = 0
            if keyword == "多晶硅":
                base_value = 2500
            elif keyword == "多晶硅期货":
                base_value = 1800
            elif keyword == "PS期货":
                base_value = 1200
            elif keyword == "光伏硅料":
                base_value = 900
            elif keyword == "硅料价格":
                base_value = 700
            
            # 添加随机波动 (-10% 到 +10%)
            random_factor = 1 + (random.random() * 0.2 - 0.1)
            value = base_value * random_factor
            
            # 存储数据
            cursor = self.conn.cursor()
            cursor.execute(
                "INSERT INTO heat_data (date, source, keyword, value) VALUES (?, ?, ?, ?)",
                (today, "baidu_index", keyword, value)
            )
            self.conn.commit()
        
        print("搜索引擎数据收集完成")
    
    def collect_news_data(self):
        """收集新闻数据"""
        print("正在收集新闻数据...")
        
        # 模拟东方财富网新闻数据
        today = datetime.now().strftime("%Y-%m-%d")
        
        # 模拟新闻标题
        news_titles = [
            "多晶硅价格连续下跌，光伏产业链承压",
            "多晶硅期货成交量创新高，市场关注度提升",
            "西南地区丰水期临近，多晶硅生产成本有望下降",
            "多晶硅产能过剩问题加剧，行业洗牌在即",
            "中美关税谈判新进展，多晶硅出口压力缓解",
            "多晶硅期货PS2506合约大幅波动，机构与散户博弈加剧",
            "永安期货增持多晶硅期货多单，市场看多情绪升温",
            "国内多晶硅库存持续高位，价格承压下行",
            "光伏装机需求不及预期，多晶硅消费不足",
            "多晶硅期货上市一周年，市场流动性显著提升"
        ]
        
        # 模拟新闻来源
        news_sources = ["东方财富网", "新浪财经", "金十数据", "第一财经", "证券时报"]
        
        # 生成模拟新闻数据
        for i in range(15):  # 假设每天有15条相关新闻
            title = random.choice(news_titles)
            source = random.choice(news_sources)
            url = f"https://example.com/news/{i}"
            
            # 情感分析
            s = SnowNLP(title)
            sentiment = s.sentiments
            
            # 存储数据
            cursor = self.conn.cursor()
            cursor.execute(
                "INSERT INTO news_data (date, source, title, url, sentiment) VALUES (?, ?, ?, ?, ?)",
                (today, source, title, url, sentiment)
            )
            self.conn.commit()
        
        print("新闻数据收集完成")
    
    def collect_social_data(self):
        """收集社交媒体数据"""
        print("正在收集社交媒体数据...")
        
        # 模拟社交媒体数据
        today = datetime.now().strftime("%Y-%m-%d")
        
        # 模拟平台和内容类型
        platforms = ["微博", "抖音", "知乎", "B站"]
        content_types = ["讨论量", "视频量", "阅读量", "搜索量"]
        
        # 生成模拟社交媒体数据
        for platform in platforms:
            for content_type in content_types:
                # 基础值
                base_count = 0
                if platform == "微博":
                    base_count = 3500 if content_type == "讨论量" else 2800
                elif platform == "抖音":
                    base_count = 5000 if content_type == "视频量" else 4200
                elif platform == "知乎":
                    base_count = 2000 if content_type == "阅读量" else 1500
                elif platform == "B站":
                    base_count = 1800 if content_type == "视频量" else 1200
                
                # 添加随机波动 (-15% 到 +15%)
                random_factor = 1 + (random.random() * 0.3 - 0.15)
                count = int(base_count * random_factor)
                
                # 模拟情感得分 (0.3 到 0.7 之间)
                sentiment = 0.3 + random.random() * 0.4
                
                # 存储数据
                cursor = self.conn.cursor()
                cursor.execute(
                    "INSERT INTO social_data (date, platform, content_type, count, sentiment) VALUES (?, ?, ?, ?, ?)",
                    (today, platform, content_type, count, sentiment)
                )
                self.conn.commit()
        
        print("社交媒体数据收集完成")
    
    def calculate_daily_heat_index(self):
        """计算每日热度指数"""
        print("正在计算每日热度指数...")
        
        today = datetime.now().strftime("%Y-%m-%d")
        cursor = self.conn.cursor()
        
        # 计算搜索热度 (30%)
        cursor.execute("SELECT AVG(value) FROM heat_data WHERE date = ? AND source = 'baidu_index'", (today,))
        search_heat = cursor.fetchone()[0] * 0.3
        
        # 计算社交媒体热度 (25%)
        cursor.execute("SELECT AVG(count) FROM social_data WHERE date = ? AND platform IN ('微博', '抖音')", (today,))
        social_heat = cursor.fetchone()[0] * 0.25
        
        # 计算新闻热度 (25%)
        cursor.execute("SELECT COUNT(*) FROM news_data WHERE date = ?", (today,))
        news_count = cursor.fetchone()[0]
        news_heat = news_count * 50 * 0.25  # 假设每条新闻价值50点热度
        
        # 计算论坛热度 (10%)
        forum_heat = (social_heat * 0.5) * 0.1  # 假设论坛热度为社交媒体热度的一半
        
        # 计算视频热度 (10%)
        cursor.execute("SELECT AVG(count) FROM social_data WHERE date = ? AND platform IN ('B站') AND content_type = '视频量'", (today,))
        video_result = cursor.fetchone()[0]
        video_heat = (video_result if video_result else 0) * 0.1
        
        # 计算总热度
        total_heat = search_heat + social_heat + news_heat + forum_heat + video_heat
        
        # 计算平均情感得分
        cursor.execute("SELECT AVG(sentiment) FROM news_data WHERE date = ?", (today,))
        news_sentiment = cursor.fetchone()[0]
        
        cursor.execute("SELECT AVG(sentiment) FROM social_data WHERE date = ?", (today,))
        social_sentiment = cursor.fetchone()[0]
        
        sentiment = (news_sentiment * 0.6 + social_sentiment * 0.4)  # 新闻情感权重更高
        
        # 存储每日热度指数
        cursor.execute(
            "INSERT INTO daily_heat_index (date, total_heat, search_heat, social_heat, news_heat, forum_heat, video_heat, sentiment) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            (today, total_heat, search_heat, social_heat, news_heat, forum_heat, video_heat, sentiment)
        )
        self.conn.commit()
        
        print("每日热度指数计算完成")
        
        return {
            "date": today,
            "total_heat": total_heat,
            "search_heat": search_heat,
            "social_heat": social_heat,
            "news_heat": news_heat,
            "forum_heat": forum_heat,
            "video_heat": video_heat,
            "sentiment": sentiment
        }
    
    def generate_historical_data(self, days=30):
        """生成历史数据用于趋势分析"""
        print(f"正在生成{days}天的历史数据...")
        
        # 清空历史数据
        cursor = self.conn.cursor()
        cursor.execute("DELETE FROM daily_heat_index WHERE date != ?", (datetime.now().strftime("%Y-%m-%d"),))
        self.conn.commit()
        
        # 生成历史数据
        for i in range(1, days + 1):
            date = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
            
            # 基础热度值 (假设有一个基本趋势)
            base_heat = 5000 + i * 50  # 假设热度在逐渐上升
            
            # 添加周期性波动 (周末热度较低)
            day_of_week = (datetime.now() - timedelta(days=i)).weekday()
            if day_of_week >= 5:  # 周末
                base_heat *= 0.8
            
            # 添加随机波动
            random_factor = 1 + (random.random() * 0.2 - 0.1)
            total_heat = base_heat * random_factor
            
            # 分配到各个热度分量
            search_heat = total_heat * 0.3
            social_heat = total_heat * 0.25
            news_heat = total_heat * 0.25
            forum_heat = total_heat * 0.1
            video_heat = total_heat * 0.1
            
            # 情感得分 (假设在0.4到0.6之间波动)
            sentiment = 0.4 + random.random() * 0.2
            
            # 存储数据
            cursor.execute(
                "INSERT INTO daily_heat_index (date, total_heat, search_heat, social_heat, news_heat, forum_heat, video_heat, sentiment) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                (date, total_heat, search_heat, social_heat, news_heat, forum_heat, video_heat, sentiment)
            )
        
        self.conn.commit()
        print("历史数据生成完成")
    
    def analyze_heat_trend(self, days=30):
        """分析热度趋势"""
        print("正在分析热度趋势...")
        
        cursor = self.conn.cursor()
        cursor.execute("SELECT date, total_heat, sentiment FROM daily_heat_index ORDER BY date DESC LIMIT ?", (days,))
        results = cursor.fetchall()
        
        # 转换为DataFrame
        df = pd.DataFrame(results, columns=["date", "total_heat", "sentiment"])
        df["date"] = pd.to_datetime(df["date"])
        df = df.sort_values("date")
        
        # 计算7日移动平均
        df["7d_ma"] = df["total_heat"].rolling(window=7).mean()
        
        # 计算热度变化率
        df["heat_change"] = df["total_heat"].pct_change() * 100
        
        # 判断趋势
        latest_heat = df["total_heat"].iloc[-1]
        avg_7d_heat = df.iloc[-7:]["total_heat"].mean()
        avg_30d_heat = df["total_heat"].mean()
        
        if latest_heat > avg_7d_heat > avg_30d_heat:
            trend = "强势上升"
        elif latest_heat > avg_7d_heat:
            trend = "短期上升"
        elif latest_heat < avg_7d_heat < avg_30d_heat:
            trend = "强势下降"
        elif latest_heat < avg_7d_heat:
            trend = "短期下降"
        else:
            trend = "震荡"
        
        # 计算最近一周的平均情感得分
        avg_sentiment = df.iloc[-7:]["sentiment"].mean()
        
        return {
            "trend": trend,
            "latest_heat": latest_heat,
            "avg_7d_heat": avg_7d_heat,
            "avg_30d_heat": avg_30d_heat,
            "heat_change": df["heat_change"].iloc[-1],
            "avg_sentiment": avg_sentiment,
            "heat_data": df
        }
    
    def generate_daily_report(self):
        """生成每日热度分析报告"""
        print("正在生成每日热度分析报告...")
        
        # 获取今日热度数据
        today_data = self.calculate_daily_heat_index()
        
        # 分析热度趋势
        trend_analysis = self.analyze_heat_trend()
        
        # 获取热门新闻
        cursor = self.conn.cursor()
        cursor.execute("SELECT title, source, sentiment FROM news_data WHERE date = ? ORDER BY sentiment DESC LIMIT 5", (today_data["date"],))
        top_news = cursor.fetchall()
        
        # 生成报告内容
        report_content = f"# 多晶硅期货热度分析日报 ({today_data['date']})\n\n"
        
        # 添加热度指标概览
        report_content += "## 一、热度指标概览\n\n"
        report_content += f"**综合热度指标**: {today_data['total_heat']:.2f}\n"
        report_content += f"**搜索引擎热度**: {today_data['search_heat']:.2f} (占比: 30%)\n"
        report_content += f"**社交媒体热度**: {today_data['social_heat']:.2f} (占比: 25%)\n"
        report_content += f"**财经媒体报道热度**: {today_data['news_heat']:.2f} (占比: 25%)\n"
        report_content += f"**行业论坛讨论热度**: {today_data['forum_heat']:.2f} (占比: 10%)\n"
        report_content += f"**视频平台热度**: {today_data['video_heat']:.2f} (占比: 10%)\n"
        report_content += f"**市场情感得分**: {today_data['sentiment']:.2f} (0-1之间，越接近1越正面)\n\n"
        
        # 添加热度趋势分析
        report_content += "## 二、热度趋势分析\n\n"
        report_content += f"**热度趋势判断**: {trend_analysis['trend']}\n"
        report_content += f"**日热度变化**: {trend_analysis['heat_change']:.2f}%\n"
        report_content += f"**7日平均热度**: {trend_analysis['avg_7d_heat']:.2f}\n"
        report_content += f"**30日平均热度**: {trend_analysis['avg_30d_heat']:.2f}\n"
        report_content += f"**7日平均情感得分**: {trend_analysis['avg_sentiment']:.2f}\n\n"
        
        # 添加热点新闻分析
        report_content += "## 三、热点新闻分析\n\n"
        report_content += "**今日热门新闻**:\n\n"
        
        for i, (title, source, sentiment) in enumerate(top_news, 1):
            sentiment_desc = "正面" if sentiment > 0.6 else "中性" if sentiment > 0.4 else "负面"
            report_content += f"{i}. {title} (来源: {source}, 情感: {sentiment_desc})\n"
        
        report_content += "\n"
        
        # 添加市场影响分析
        report_content += "## 四、市场影响分析\n\n"
        
        if trend_analysis['trend'] in ["强势上升", "短期上升"]:
            if trend_analysis['avg_sentiment'] > 0.55:
                report_content += "**市场分析**: 热度快速上升且情感偏正面，表明市场关注度提高，可能带来短期价格上涨压力。\n"
            else:
                report_content += "**市场分析**: 热度上升但情感中性或偏负面，表明市场担忧情绪增加，可能是负面消息引发的关注。\n"
        elif trend_analysis['trend'] in ["强势下降", "短期下降"]:
            if trend_analysis['avg_sentiment'] < 0.45:
                report_content += "**市场分析**: 热度下降且情感偏负面，表明市场兴趣减弱，可能伴随价格下行压力。\n"
            else:
                report_content += "**市场分析**: 热度下降但情感中性或偏正面，表明市场关注点可能转移，但基本面预期仍较稳定。\n"
        else:
            report_content += "**市场分析**: 热度震荡，表明市场处于观望状态，短期内可能维持区间波动。\n"
        
        # 添加交易建议
        report_content += "\n**交易建议**:\n"
        
        if trend_analysis['trend'] in ["强势上升", "短期上升"] and trend_analysis['avg_sentiment'] > 0.55:
            report_content += "- 关注热度持续上升带来的短期做多机会\n"
            report_content += "- 警惕热度过高可能引发的回调风险\n"
            report_content += "- 建议在技术面支撑位附近逢低布局\n"
        elif trend_analysis['trend'] in ["强势下降", "短期下降"] and trend_analysis['avg_sentiment'] < 0.45:
            report_content += "- 短期内可能面临下行压力，建议控制多头仓位\n"
            report_content += "- 可考虑在技术面阻力位附近逢高做空\n"
            report_content += "- 关注热度企稳回升信号\n"
        else:
            report_content += "- 市场情绪较为中性，建议等待更明确的方向性信号\n"
            report_content += "- 可采取区间交易策略，在支撑位买入，阻力位卖出\n"
            report_content += "- 密切关注热度和情感指标的变化\n"
        
        # 保存报告
        report_file = os.path.join(self.report_dir, f"polysilicon_heat_report_{today_data['date']}.md")
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(report_content)
        
        print(f"每日热度分析报告已生成: {report_file}")
        
        return report_content
    
    def run(self):
        """运行完整的热度分析流程"""
        # 收集数据
        self.collect_search_data()
        self.collect_news_data()
        self.collect_social_data()
        
        # 生成历史数据用于趋势分析
        self.generate_historical_data()
        
        # 生成每日报告
        report = self.generate_daily_report()
        
        # 关闭数据库连接
        self.conn.close()
        
        return report

# 运行分析器
if __name__ == "__main__":
    analyzer = PolyiliconHeatAnalyzer()
    report = analyzer.run()
    print("\n报告预览:\n")
    print(report[:500] + "...\n")
    print(f"完整报告已保存到 reports 目录")
