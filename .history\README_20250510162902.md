# 期货实时监控系统

这是一个期货实时监控系统，可以通过两种方式获取实时价格数据，并根据预设策略生成交易信号：

1. **屏幕识别方案**：从文华财经软件界面读取价格数据
2. **API方案**：通过AKShare等API获取实时价格数据

## 功能特点

- 支持两种数据获取方式：屏幕识别和API接口
- 支持自定义交易策略和条件
- 基于ATR的动态止盈止损策略
- 分批止盈，保护利润
- 自动生成交易信号，减少情绪因素影响

## 系统要求

### 共同要求
- Python 3.7+
- pandas, numpy等基础库

### 屏幕识别方案额外要求
- 文华财经软件（WH6或WT9）
- Tesseract OCR（用于识别屏幕文字）

### API方案额外要求
- AKShare库（用于获取期货实时数据）
- 网络连接

## 安装步骤

1. 安装Python依赖包：

```bash
pip install -r requirements.txt
```

2. 安装Tesseract OCR：

- Windows: 从[这里](https://github.com/UB-Mannheim/tesseract/wiki)下载并安装
- 安装时请选择"Additional language data"以支持中文识别

3. 配置Tesseract路径：

在`screen_capture.py`文件中修改Tesseract路径：

```python
tesseract_path = r'C:\Program Files\Tesseract-OCR\tesseract.exe'  # 根据实际安装路径修改
```

## 使用方法

### 选择数据获取方式

本系统提供两种数据获取方式，您可以根据需要选择其中一种：

#### 方案1：屏幕识别方式

##### 步骤1：校准屏幕区域

首先需要校准文华财经软件界面上各个价格数据的位置：

1. 打开文华财经软件并显示期货行情
2. 运行校准工具：

```bash
python calibrate_regions.py
```

3. 按照提示框选各个价格区域（合约代码、最新价、开盘价、最高价、最低价、成交量）
4. 点击"保存配置"按钮保存区域设置
5. 点击"测试配置"按钮验证识别效果

#### 方案2：API方式

API方式不需要校准屏幕区域，直接使用AKShare库获取实时数据。

### 步骤2：配置监控策略

根据需要修改`monitor_config.json`文件，配置交易策略和条件：

```json
{
    "check_interval": 60,  // 检查间隔，单位秒
    "trading_hours": [     // 交易时间
        {"start": "09:00:00", "end": "11:30:00"},
        {"start": "13:30:00", "end": "15:00:00"}
    ],
    "strategies": {
        "atr_strategy": {
            "enabled": true,
            "atr_period": 14,
            "atr_multiplier": 2.5,
            "entry_rules": {
                "short": {
                    "price_condition": "price > previous_high",
                    "atr_condition": "price > ma20 + atr * 0.5"
                }
            },
            "exit_rules": {
                "short": {
                    "take_profit": [
                        {"percent": 0.3, "target": "entry_price - atr * 1.5"},
                        {"percent": 0.3, "target": "entry_price - atr * 2.5"},
                        {"percent": 0.2, "target": "entry_price - atr * 4.0"}
                    ],
                    "stop_loss": "entry_price + atr * 2.5"
                }
            }
        }
    }
}
```

### 步骤3：运行监控系统

1. 确保文华财经软件已打开并显示期货行情
2. 运行监控系统：

```bash
python futures_monitor_ocr.py
```

3. 系统将按照设定的时间间隔检查价格，并在满足条件时生成交易信号
4. 交易信号将显示在控制台上，并记录在日志文件中

## 文件说明

- `calibrate_regions.py`: 屏幕区域校准工具
- `screen_capture.py`: 屏幕截图和OCR识别模块
- `futures_monitor_ocr.py`: 主监控程序
- `monitor_config.json`: 监控系统配置文件
- `screen_regions.json`: 屏幕区域配置文件（由校准工具生成）
- `price_data.csv`: 价格数据保存文件
- `futures_monitor.log`: 日志文件

## 注意事项

1. 屏幕识别的准确性受多种因素影响，如屏幕分辨率、字体大小、背景色等，可能需要多次调整校准区域以获得最佳效果
2. 建议在使用前先进行充分测试，确保识别准确率
3. 系统生成的交易信号仅供参考，实际交易决策应结合市场情况和个人判断
4. 在交易时间内，请保持文华财经软件窗口可见，不要遮挡价格区域

## 常见问题

1. **识别不准确怎么办？**
   - 调整校准区域，确保只包含价格数字部分
   - 尝试调整文华软件的字体大小和颜色设置
   - 检查Tesseract OCR是否正确安装并配置

2. **系统运行缓慢怎么办？**
   - 增加检查间隔时间
   - 减少截图区域大小
   - 关闭其他占用资源的程序

3. **如何添加自定义策略？**
   - 在`monitor_config.json`文件中添加新的策略配置
   - 修改`futures_monitor_ocr.py`文件，添加自定义策略逻辑

## 后续开发计划

- 添加图形用户界面，方便操作和查看信号
- 支持多合约同时监控
- 添加更多技术指标和策略模板
- 支持通过邮件或短信通知交易信号
- 集成自动交易功能（需要交易API支持）
