# 机构持仓数据制作指引

## 基本格式要求

### 1. 文件标题格式
- 使用格式：`# 机构持仓数据 - YYYY年MM月DD日`（如：`# 机构持仓数据 - 2024年6月10日`）

### 2. 合约章节格式
每个合约使用二级标题：
- `## 多晶硅2507合约`
- `## 多晶硅2508合约` 
- `## 多晶硅2509合约`
- `## 多晶硅2511合约`

### 3. 表格格式要求
必须包含以下6个列：

| 会员简称 | 净持仓 | 胜率（统计次数） | 增减 | 成交量 | 净持仓换手率 |
|----------|--------|------------------|------|--------|-------------|

### 4. 分隔线格式
每个合约表格后添加分隔线：
```
---
```

## 数据处理规则

### 1. 净持仓换手率计算
公式：`净持仓换手率 = 成交量 ÷ |净持仓| × 100%`
- 保留2位小数
- 对于净持仓为负数的，取绝对值计算

### 2. 成交量数据处理
- 有显示数据的：直接使用
- 无显示数据的：使用格式 `＜[最小成交量]`
- 各合约最小成交量参考值：
  - 2507合约：通常3000-4000左右
  - 2508合约：通常500-700左右  
  - 2509合约：通常400-500左右
  - 2511合约：通常150-250左右

### 3. 换手率格式
- 有成交量数据的：直接计算，如 `95.10%`
- 无成交量数据的：使用格式 `＜XXX.XX%`

## 制作步骤

### 第一步：创建基础表格
1. 根据持仓数据图片，提取：会员简称、净持仓、胜率（统计次数）、增减
2. 创建初始表格，只包含前4列
3. 按净持仓从大到小排序

### 第二步：添加成交量数据
1. 根据成交量数据图片，匹配对应机构
2. 添加成交量列
3. 对于未显示成交量的机构，查找该合约最小显示成交量作为参考

### 第三步：计算换手率
1. 对于有确切成交量的机构，直接计算
2. 对于使用最小成交量估算的机构，使用"＜"格式

### 第四步：制作汇总表
1. 创建期货公司持仓明细汇总表
2. 汇总每个期货公司在所有合约中的数据：
   - 全合约净持仓总和：该公司在所有合约的净持仓相加
   - 全合约净持仓增减总和：该公司在所有合约的增减相加
   - 全合约成交量总和：该公司在所有合约的成交量相加
   - 全合约净持仓换手率：成交量总和 ÷ |净持仓总和| × 100%
   - 持仓胜率：使用该公司最主要合约的胜率
3. 按净持仓总和从大到小排序

### 第五步：格式检查
1. 确保标题格式正确
2. 确保每个合约后有分隔线 `---`
3. 确保表格对齐
4. 确保数据计算准确
5. 确保汇总表计算准确

## 常见错误检查清单

- [ ] 文件标题是否包含正确日期
- [ ] 是否包含所有显示的合约（2507、2508、2509、2511等）
- [ ] 表格是否包含全部6列
- [ ] 每个合约后是否有分隔线
- [ ] 换手率计算是否正确
- [ ] 成交量缺失数据是否使用＜格式
- [ ] 数据排序是否按净持仓从大到小
- [ ] **⭐ 是否包含期货公司持仓明细汇总表（按公司汇总所有合约数据）**

## 重要提醒

1. **必须仔细对比前一天的文件格式**，确保格式一致
2. **分隔线不能遗漏**，这是格式要求的重要部分
3. **日期格式要准确**，使用中文格式"YYYY年MM月DD日"
4. **所有合约数据都要包含**，不能遗漏任何一个合约
5. **换手率计算要准确**，特别注意净持仓为负数时取绝对值
6. **⚠️ 最重要：必须在文件末尾添加期货公司持仓明细汇总表！！！**
   - 汇总表标题：`## 期货公司持仓明细汇总表`
   - 汇总表列：期货公司、全合约净持仓总和、全合约净持仓增减总和、全合约成交量总和、全合约净持仓换手率、持仓胜率
   - 按每个期货公司在所有合约中的数据进行汇总计算 