# 多晶硅期货热度分析系统

这是一个用于分析多晶硅期货市场热度的系统，通过收集和分析各种数据源的信息，生成每日热度分析报告，帮助交易者了解市场关注度和情绪变化。

## 功能特点

- 收集多个数据源的多晶硅相关信息
  - 新闻数据（NewsAPI、东方财富网等）
  - 社交媒体数据（微博、知乎等）
  - 搜索引擎数据（百度指数等）
- 计算综合热度指标
- 生成每日热度分析报告
- 分析热度与市场价格的关系

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 首先，需要设置相关API密钥（如果使用）：
   - 在`polysilicon_heat_analyzer_v2.py`文件中设置`newsapi_key`、`gnews_key`等API密钥

2. 运行分析器：
   ```bash
   python polysilicon_heat_analyzer_v2.py
   ```

3. 查看生成的报告：
   - 报告将保存在`reports`目录下
   - 文件名格式为`polysilicon_heat_report_YYYY-MM-DD.md`

## 数据源说明

1. **新闻数据**
   - NewsAPI: 获取全球新闻数据
   - 东方财富网: 获取财经新闻数据

2. **社交媒体数据**
   - 微博: 获取热搜和讨论数据
   - 知乎: 获取热榜和问答数据

3. **搜索引擎数据**
   - 百度指数: 获取关键词搜索热度

## 热度指标计算方法

综合热度指标由以下几个部分组成：
- 搜索引擎热度 (30%)
- 社交媒体热度 (25%)
- 财经媒体报道热度 (25%)
- 行业论坛讨论热度 (10%)
- 视频平台热度 (10%)

## 报告内容

每日热度分析报告包含以下内容：
1. 热度指标概览
2. 热点新闻分析
3. 市场影响分析

## 注意事项

- 部分数据源需要API密钥才能访问
- 百度指数等数据可能需要登录才能获取
- 网页爬虫可能受到网站反爬虫措施的影响

## 未来改进计划

- 添加更多数据源
- 实现热度与价格的相关性分析
- 添加情感分析功能
- 实现自动化定时运行
- 添加可视化图表
