import os
import sys
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('monitor.log')
    ]
)
logger = logging.getLogger("SimpleMonitor")

class SimpleMonitor:
    """简单期货监控系统，使用本地CSV数据"""

    def __init__(self, csv_file="PSFUTURES2025.csv"):
        """初始化监控系统"""
        self.csv_file = csv_file
        self.data = None
        self.indicators = {}
        self.signals = []
        self.positions = []

        # 加载数据
        self._load_data()

        # 计算指标
        self._calculate_indicators()

    def _load_data(self):
        """加载CSV数据"""
        try:
            if not os.path.exists(self.csv_file):
                logger.error(f"找不到文件: {self.csv_file}")
                return

            # 读取CSV文件
            self.data = pd.read_csv(self.csv_file, encoding='utf-8', skiprows=1)
            logger.info(f"成功加载数据，共{len(self.data)}条记录")

            # 数据预处理
            # 确保数值列为浮点型
            numeric_columns = ['开盘价', '最高价', '最低价', '收盘价', '前结算价', '结算价', '涨跌', '涨跌1', '成交量', '持仓量']
            for col in numeric_columns:
                if col in self.data.columns:
                    self.data[col] = pd.to_numeric(self.data[col], errors='coerce')

            # 按日期排序
            self.data = self.data.sort_values(by='交易日期')

            # 提取最新数据
            self.latest_data = self.data.iloc[-1].to_dict()
            logger.info(f"最新数据日期: {self.latest_data['交易日期']}")

        except Exception as e:
            logger.error(f"加载数据出错: {e}")

    def _calculate_indicators(self):
        """计算技术指标"""
        try:
            if self.data is None:
                logger.error("无法计算指标，数据为空")
                return

            # 计算ATR
            high = self.data['最高价']
            low = self.data['最低价']
            close = self.data['收盘价']

            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = tr.rolling(window=14).mean()

            self.indicators['atr'] = atr.iloc[-1]
            self.indicators['atr_percentile'] = np.percentile(atr.dropna(), [25, 50, 75, 90])

            # 计算移动平均线
            self.indicators['ma5'] = close.rolling(window=5).mean().iloc[-1]
            self.indicators['ma10'] = close.rolling(window=10).mean().iloc[-1]
            self.indicators['ma20'] = close.rolling(window=20).mean().iloc[-1]
            self.indicators['ma60'] = close.rolling(window=60).mean().iloc[-1]

            # 计算前一日数据
            self.indicators['previous_close'] = close.iloc[-2]
            self.indicators['previous_high'] = high.iloc[-2]
            self.indicators['previous_low'] = low.iloc[-2]

            # 计算波动率
            self.indicators['volatility'] = close.pct_change().rolling(window=20).std().iloc[-1] * 100

            logger.info(f"成功计算技术指标: {self.indicators}")

        except Exception as e:
            logger.error(f"计算指标出错: {e}")

    def simulate_realtime_data(self, iteration=0):
        """模拟实时数据"""
        # 在实际应用中，这里应该从实时数据源获取数据
        # 这里我们使用最新的历史数据加上一些随机波动来模拟

        latest = self.latest_data

        # 根据迭代次数生成不同的价格变化，以触发不同的信号
        if iteration < 3:
            # 前3次迭代，价格上涨，可能触发做空入场信号
            price_change = self.indicators['atr'] * 0.8 * (iteration + 1)
            simulated_price = latest['收盘价'] + price_change
        elif iteration < 6:
            # 第4-6次迭代，价格大幅下跌，可能触发止盈信号
            price_change = -self.indicators['atr'] * 1.0 * (iteration - 2)
            simulated_price = latest['收盘价'] + price_change
        else:
            # 第7-10次迭代，价格大幅上涨，可能触发止损信号
            price_change = self.indicators['atr'] * 1.5 * (iteration - 6)
            simulated_price = latest['收盘价'] + price_change

        simulated_data = {
            'symbol': 'ps2506',
            'last_price': simulated_price,
            'open': latest['开盘价'],
            'high': max(latest['最高价'], simulated_price),
            'low': min(latest['最低价'], simulated_price),
            'volume': latest['成交量'],
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        }

        logger.info(f"模拟实时数据: {simulated_data}")
        return simulated_data

    def check_entry_signals(self, price_data):
        """检查入场信号"""
        signals = []

        # 准备数据
        data = {
            'price': price_data['last_price'],
            'ma20': self.indicators['ma20'],
            'atr': self.indicators['atr'],
            'previous_high': self.indicators['previous_high'],
            'previous_low': self.indicators['previous_low'],
        }

        # 做空入场条件
        # 1. 价格高于前一日最高价
        # 2. 价格高于20日均线加0.5倍ATR
        if (data['price'] > data['previous_high'] and
            data['price'] > data['ma20'] + data['atr'] * 0.5):

            signal = {
                'type': 'ENTRY',
                'direction': 'SHORT',
                'price': data['price'],
                'time': price_data['timestamp'],
                'reason': '价格突破前日高点且高于MA20+0.5ATR',
            }
            signals.append(signal)
            logger.info(f"生成做空入场信号: {signal}")

        # 做多入场条件
        # 1. 价格低于前一日最低价
        # 2. 价格低于20日均线减0.5倍ATR
        if (data['price'] < data['previous_low'] and
            data['price'] < data['ma20'] - data['atr'] * 0.5):

            signal = {
                'type': 'ENTRY',
                'direction': 'LONG',
                'price': data['price'],
                'time': price_data['timestamp'],
                'reason': '价格跌破前日低点且低于MA20-0.5ATR',
            }
            signals.append(signal)
            logger.info(f"生成做多入场信号: {signal}")

        return signals

    def check_exit_signals(self, price_data, positions):
        """检查出场信号"""
        signals = []

        for position in positions:
            direction = position['direction']
            entry_price = position['entry_price']

            # 准备数据
            data = {
                'price': price_data['last_price'],
                'entry_price': entry_price,
                'atr': self.indicators['atr'],
            }

            # 做空出场条件
            if direction == 'SHORT':
                # 止损: 价格上涨超过入场价加2.5倍ATR
                stop_loss_price = entry_price + self.indicators['atr'] * 2.5
                if data['price'] >= stop_loss_price:
                    signal = {
                        'type': 'EXIT',
                        'direction': 'SHORT',
                        'price': data['price'],
                        'time': price_data['timestamp'],
                        'reason': '触发止损',
                        'percent': 1.0,  # 全部平仓
                    }
                    signals.append(signal)
                    logger.info(f"生成做空止损信号: {signal}")
                    continue  # 止损信号优先级最高

                # 止盈1: 价格下跌超过入场价减1.5倍ATR
                take_profit1_price = entry_price - self.indicators['atr'] * 1.5
                if data['price'] <= take_profit1_price:
                    signal = {
                        'type': 'EXIT',
                        'direction': 'SHORT',
                        'price': data['price'],
                        'time': price_data['timestamp'],
                        'reason': '触发第一级止盈',
                        'percent': 0.3,  # 平仓30%
                    }
                    signals.append(signal)
                    logger.info(f"生成做空止盈信号: {signal}")

                # 止盈2: 价格下跌超过入场价减2.5倍ATR
                take_profit2_price = entry_price - self.indicators['atr'] * 2.5
                if data['price'] <= take_profit2_price:
                    signal = {
                        'type': 'EXIT',
                        'direction': 'SHORT',
                        'price': data['price'],
                        'time': price_data['timestamp'],
                        'reason': '触发第二级止盈',
                        'percent': 0.3,  # 平仓30%
                    }
                    signals.append(signal)
                    logger.info(f"生成做空止盈信号: {signal}")

                # 止盈3: 价格下跌超过入场价减4.0倍ATR
                take_profit3_price = entry_price - self.indicators['atr'] * 4.0
                if data['price'] <= take_profit3_price:
                    signal = {
                        'type': 'EXIT',
                        'direction': 'SHORT',
                        'price': data['price'],
                        'time': price_data['timestamp'],
                        'reason': '触发第三级止盈',
                        'percent': 0.2,  # 平仓20%
                    }
                    signals.append(signal)
                    logger.info(f"生成做空止盈信号: {signal}")

            # 做多出场条件类似，这里省略

        return signals

    def run_simulation(self, num_iterations=10):
        """运行模拟测试"""
        logger.info("开始运行模拟测试")

        # 模拟持仓
        self.positions = [
            {
                'direction': 'SHORT',
                'entry_price': 37000,
                'quantity': 5,
                'entry_time': '2025-05-08 10:30:00',
            }
        ]

        for i in range(num_iterations):
            logger.info(f"模拟迭代 {i+1}/{num_iterations}")

            # 模拟实时数据
            price_data = self.simulate_realtime_data(iteration=i)

            # 检查入场信号
            entry_signals = self.check_entry_signals(price_data)

            # 检查出场信号
            exit_signals = self.check_exit_signals(price_data, self.positions)

            # 合并信号
            all_signals = entry_signals + exit_signals

            # 输出信号
            if all_signals:
                print(f"\n===== 检测到 {len(all_signals)} 个交易信号 =====")
                for signal in all_signals:
                    print(f"信号类型: {signal['type']} {signal['direction']}")
                    print(f"价格: {signal['price']:.2f}")
                    print(f"时间: {signal['time']}")
                    print(f"原因: {signal['reason']}")
                    if 'percent' in signal:
                        print(f"比例: {signal['percent']*100:.0f}%")
                    print("-" * 40)

            # 等待一下
            time.sleep(1)

        logger.info("模拟测试完成")

if __name__ == "__main__":
    # 创建监控系统
    monitor = SimpleMonitor()

    # 运行模拟测试
    monitor.run_simulation(num_iterations=10)
