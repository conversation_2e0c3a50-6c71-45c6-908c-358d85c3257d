"""
测试市场分析模块
"""

import os
import pandas as pd
from market_analysis import MarketAnalyzer

def create_test_data():
    """创建测试数据"""
    # 创建一个简单的测试数据集
    data = {
        '交易日期': [f'2025{str(i).zfill(4)}' for i in range(1, 61)],
        '开盘价': [35000 + i * 100 for i in range(60)],
        '最高价': [35500 + i * 100 for i in range(60)],
        '最低价': [34500 + i * 100 for i in range(60)],
        '收盘价': [35200 + i * 100 for i in range(60)],
        '成交量': [1000 + i * 10 for i in range(60)],
        '持仓量': [5000 + i * 20 for i in range(60)]
    }

    df = pd.DataFrame(data)

    # 保存为CSV文件
    df.to_csv('PSFUTURES2025.csv', index=False)

    print(f"测试数据已保存至 PSFUTURES2025.csv，共{len(df)}条记录")
    return df

def test_market_analyzer():
    """测试市场分析器"""
    # 创建测试数据
    if not os.path.exists('PSFUTURES2025.csv'):
        create_test_data()

    print("开始测试市场分析器...")

    # 创建市场分析器
    analyzer = MarketAnalyzer()
    print("市场分析器创建成功")

    # 设置当前持仓
    current_position = {
        "direction": "SHORT",
        "size": 5,
        "entry_price": 37000
    }
    print(f"当前持仓: {current_position}")

    # 加载数据
    print("开始加载数据...")
    success = analyzer.load_data()
    if success:
        print(f"数据加载成功，共{len(analyzer.data)}条记录")
    else:
        print("数据加载失败")
        return

    # 计算指标
    print("开始计算技术指标...")
    success = analyzer.calculate_indicators()
    if success:
        print("技术指标计算成功")
    else:
        print("技术指标计算失败")
        return

    # 分析市场
    print("开始分析市场状态...")
    success = analyzer.analyze_market()
    if success:
        print("市场状态分析成功")
    else:
        print("市场状态分析失败")
        return

    # 生成交易计划
    print("开始生成交易计划...")
    success = analyzer.generate_trade_plan(current_position)
    if success:
        print("交易计划生成成功")
    else:
        print("交易计划生成失败")
        return

    # 生成报告
    print("开始生成分析报告...")
    report_file = analyzer.generate_report()

    if report_file:
        print(f"分析报告已生成: {report_file}")

        # 打印交易计划摘要
        trade_plan = analyzer.trade_plan

        print("\n" + "="*50)
        print("交易计划摘要")
        print("="*50)

        print(f"市场偏向: {trade_plan.get('market_bias', '未知')}")
        print(f"建议方向: {trade_plan.get('recommended_direction', '未知')}")

        # 打印仓位调整建议
        position_adj = trade_plan.get("position_adjustment", {})
        print("\n仓位调整建议:")
        print(f"- 建议: {position_adj.get('recommendation', '未知')}")
        print(f"- 当前仓位: {position_adj.get('current_size', 0)}手")
        print(f"- 目标仓位: {position_adj.get('target_size', 0)}手")

        # 打印止损设置
        stop_loss = trade_plan.get("stop_loss", {})
        if stop_loss.get("price"):
            print("\n止损设置:")
            print(f"- 止损价: {stop_loss.get('price', 0):.2f}")
            print(f"- 止损点数: {stop_loss.get('points', 0):.2f}")

        # 打印止盈设置
        take_profit = trade_plan.get("take_profit", [])
        if take_profit:
            print("\n止盈设置:")
            for level in take_profit:
                print(f"- {level.get('percent', 0):.0f}%仓位: {level.get('price', 0):.2f}")

        print("="*50)
    else:
        print("分析失败")

if __name__ == "__main__":
    test_market_analyzer()
