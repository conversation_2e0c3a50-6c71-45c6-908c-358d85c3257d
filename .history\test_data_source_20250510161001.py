import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.data_manager import DataManager
from utils.logger import setup_logger

def test_realtime_data(data_manager, symbols):
    """测试实时数据获取"""
    print("\n===== 测试实时数据获取 =====")
    for symbol in symbols:
        print(f"\n获取 {symbol} 实时数据:")
        data = data_manager.get_realtime_data(symbol, exchange="GFEX")
        if data:
            print(f"  时间: {data['timestamp']}")
            print(f"  最新价: {data['last_price']}")
            print(f"  开盘价: {data['open']}")
            print(f"  最高价: {data['high']}")
            print(f"  最低价: {data['low']}")
            print(f"  成交量: {data['volume']}")
        else:
            print(f"  获取 {symbol} 实时数据失败")

def test_historical_data(data_manager, symbols):
    """测试历史数据获取"""
    print("\n===== 测试历史数据获取 =====")
    for symbol in symbols:
        print(f"\n获取 {symbol} 历史数据:")
        data = data_manager.get_historical_data(symbol)
        if data is not None and not data.empty:
            print(f"  数据条数: {len(data)}")
            print(f"  最新数据: \n{data.head(3)}")
        else:
            print(f"  获取 {symbol} 历史数据失败")

def test_indicators(data_manager, symbols):
    """测试技术指标计算"""
    print("\n===== 测试技术指标计算 =====")
    for symbol in symbols:
        print(f"\n计算 {symbol} 技术指标:")
        indicators = data_manager.calculate_indicators(symbol)
        if indicators:
            for key, value in indicators.items():
                print(f"  {key}: {value}")
        else:
            print(f"  计算 {symbol} 技术指标失败")

def main():
    """主函数"""
    # 设置日志
    logger = setup_logger(log_level="INFO")
    logger.info("开始测试数据源")

    # 创建数据管理器
    data_manager = DataManager(primary_source="akshare")

    # 测试的期货合约
    symbols = ["ps2506"]  # 多晶硅2506合约

    # 检查本地CSV文件
    if os.path.exists("PSFUTURES2025.csv"):
        print(f"找到本地数据文件: PSFUTURES2025.csv")
    else:
        print(f"警告: 未找到本地数据文件 PSFUTURES2025.csv")

    # 测试实时数据获取
    test_realtime_data(data_manager, symbols)

    # 测试历史数据获取
    test_historical_data(data_manager, symbols)

    # 测试技术指标计算
    test_indicators(data_manager, symbols)

    logger.info("数据源测试完成")

if __name__ == "__main__":
    main()
