import logging
from .akshare_source import AKShareDataSource
from .sina_source import SinaDataSource

class DataManager:
    """数据管理器，统一管理不同的数据源"""
    
    def __init__(self, primary_source="akshare"):
        self.logger = logging.getLogger("DataManager")
        self.primary_source = primary_source
        self.sources = {
            "akshare": AKShareDataSource(),
            "sina": SinaDataSource()
        }
    
    def get_realtime_data(self, symbol, exchange=None):
        """获取实时行情数据，如果主数据源失败则尝试备用数据源"""
        self.logger.info(f"获取{symbol}实时行情数据，主数据源: {self.primary_source}")
        
        # 尝试主数据源
        data = self.sources[self.primary_source].get_realtime_data(symbol, exchange)
        
        # 如果主数据源失败，尝试备用数据源
        if data is None:
            backup_source = "sina" if self.primary_source == "akshare" else "akshare"
            self.logger.info(f"主数据源失败，尝试备用数据源: {backup_source}")
            data = self.sources[backup_source].get_realtime_data(symbol, exchange)
        
        return data
    
    def get_historical_data(self, symbol, period='daily', start_date=None, end_date=None):
        """获取历史数据"""
        self.logger.info(f"获取{symbol}历史数据，周期: {period}")
        
        # 历史数据优先使用AKShare
        data = self.sources["akshare"].get_historical_data(symbol, period, start_date, end_date)
        
        return data
    
    def calculate_indicators(self, symbol, indicators=None):
        """计算技术指标"""
        self.logger.info(f"计算{symbol}技术指标")
        
        # 技术指标计算优先使用AKShare
        data = self.sources["akshare"].calculate_indicators(symbol, indicators)
        
        return data
