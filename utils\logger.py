import logging
import sys
from logging.handlers import RotatingFileHandler
import os

def setup_logger(log_file='futures_monitor.log', log_level="INFO", max_log_size=10*1024*1024, backup_count=5):
    """设置日志"""
    # 转换日志级别
    if isinstance(log_level, str):
        log_level = getattr(logging, log_level.upper())
    
    # 创建日志目录
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 配置根日志记录器
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # 清除现有的处理程序
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建控制台处理程序
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_format)
    logger.addHandler(console_handler)
    
    # 创建文件处理程序
    file_handler = RotatingFileHandler(
        log_file, maxBytes=max_log_size, backupCount=backup_count
    )
    file_handler.setLevel(log_level)
    file_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_format)
    logger.addHandler(file_handler)
    
    return logger
