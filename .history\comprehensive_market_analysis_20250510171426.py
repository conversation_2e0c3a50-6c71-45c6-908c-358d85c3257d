"""
综合市场分析模块
结合技术面、基本面、情绪面和资金面进行全面分析
"""

import os
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import mplfinance as mpf
import json
import requests
from bs4 import BeautifulSoup
import re

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('comprehensive_analysis.log')
    ]
)
logger = logging.getLogger("ComprehensiveAnalysis")

class ComprehensiveMarketAnalyzer:
    """综合市场分析器"""
    
    def __init__(self, config_file="comprehensive_analysis_config.json"):
        """初始化"""
        self.config = self._load_config(config_file)
        self.data = None
        self.analysis_results = {}
        self.trade_plan = {}
        self.fundamental_data = {}
        self.sentiment_data = {}
        self.capital_flow_data = {}
        
        # 加载中文字体
        try:
            self.font = FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf")
        except:
            self.font = None
            logger.warning("无法加载中文字体，图表中的中文可能显示为乱码")
        
        logger.info("综合市场分析器初始化完成")
    
    def _load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "data_file": "PSFUTURES2025.csv",  # 历史数据文件
            "output_dir": "analysis_reports",   # 输出目录
            "technical_indicators": {
                "ma_periods": [5, 10, 20, 60],  # 移动平均线周期
                "rsi_period": 14,               # RSI周期
                "macd_params": [12, 26, 9],     # MACD参数
                "boll_period": 20,              # 布林带周期
                "atr_period": 14,               # ATR周期
                "kd_params": [9, 3, 3],         # KDJ参数
            },
            "overbought_oversold": {
                "rsi_overbought": 70,           # RSI超买阈值
                "rsi_oversold": 30,             # RSI超卖阈值
                "kd_overbought": 80,            # KD超买阈值
                "kd_oversold": 20,              # KD超卖阈值
            },
            "trend_thresholds": {
                "strong_uptrend": 0.05,         # 强上升趋势阈值
                "uptrend": 0.02,                # 上升趋势阈值
                "downtrend": -0.02,             # 下降趋势阈值
                "strong_downtrend": -0.05,      # 强下降趋势阈值
            },
            "fundamental_data": {
                "supply": {
                    "current": 1000000,         # 当前供应量（吨）
                    "previous": 980000,         # 上期供应量（吨）
                    "change": 20000,            # 变化量（吨）
                    "change_percent": 2.04      # 变化百分比（%）
                },
                "demand": {
                    "current": 800000,          # 当前需求量（吨）
                    "previous": 820000,         # 上期需求量（吨）
                    "change": -20000,           # 变化量（吨）
                    "change_percent": -2.44     # 变化百分比（%）
                },
                "inventory": {
                    "current": 400000,          # 当前库存量（吨）
                    "previous": 380000,         # 上期库存量（吨）
                    "change": 20000,            # 变化量（吨）
                    "change_percent": 5.26      # 变化百分比（%）
                },
                "production_cost": 30000,       # 生产成本（元/吨）
                "news": [
                    {
                        "date": "2025-05-08",
                        "title": "多晶硅产能持续扩张，供应过剩压力加大",
                        "impact": "bearish"     # 利空
                    },
                    {
                        "date": "2025-05-07",
                        "title": "光伏装机需求不及预期，多晶硅价格承压",
                        "impact": "bearish"     # 利空
                    },
                    {
                        "date": "2025-05-05",
                        "title": "部分多晶硅企业减产检修，短期供应收紧",
                        "impact": "bullish"     # 利多
                    }
                ]
            },
            "sentiment_data": {
                "market_sentiment": "bearish",  # 市场情绪：看空
                "sentiment_indicators": {
                    "put_call_ratio": 1.5,      # 看跌/看涨期权比率
                    "long_short_ratio": 0.8,    # 多/空持仓比率
                    "retail_sentiment": "bearish", # 散户情绪
                    "institutional_sentiment": "bearish" # 机构情绪
                },
                "social_media_sentiment": {
                    "positive": 30,             # 正面情绪占比（%）
                    "neutral": 20,              # 中性情绪占比（%）
                    "negative": 50              # 负面情绪占比（%）
                }
            },
            "capital_flow_data": {
                "net_flow": -50000000,          # 净资金流入（元）
                "institutional_flow": -30000000, # 机构资金流入（元）
                "retail_flow": -20000000,       # 散户资金流入（元）
                "foreign_flow": -5000000,       # 外资资金流入（元）
                "sector_rotation": "out",       # 板块资金流向：流出
                "concentration_ratio": 0.6      # 资金集中度
            },
            "position_management": {
                "max_position": 10,             # 最大持仓（手）
                "risk_per_trade": 2.0,          # 单笔风险（%）
                "stop_loss_atr_multiple": 2.5,  # 止损ATR倍数
                "take_profit_levels": [         # 止盈级别
                    {"percent": 0.3, "atr_multiple": 1.5},
                    {"percent": 0.3, "atr_multiple": 2.5},
                    {"percent": 0.2, "atr_multiple": 4.0}
                ]
            }
        }
        
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                logger.info(f"成功加载配置文件: {config_file}")
                return config
            else:
                logger.warning(f"配置文件不存在: {config_file}，使用默认配置")
                return default_config
        except Exception as e:
            logger.error(f"加载配置文件出错: {e}，使用默认配置")
            return default_config
    
    def load_data(self, data_file=None):
        """加载历史数据"""
        try:
            file_path = data_file or self.config.get("data_file")
            if not file_path:
                logger.error("未指定数据文件")
                return False
            
            if not os.path.exists(file_path):
                logger.error(f"数据文件不存在: {file_path}")
                return False
            
            # 读取CSV文件
            self.data = pd.read_csv(file_path, encoding='utf-8', skiprows=1)
            
            # 确保数值列为浮点型
            numeric_columns = ['开盘价', '最高价', '最低价', '收盘价', '前结算价', '结算价', '涨跌', '涨跌1', '成交量', '持仓量']
            for col in numeric_columns:
                if col in self.data.columns:
                    self.data[col] = pd.to_numeric(self.data[col], errors='coerce')
            
            # 按日期排序
            self.data = self.data.sort_values(by='交易日期')
            
            # 重命名列以便使用
            if '收盘价' in self.data.columns:
                self.data['close'] = self.data['收盘价']
            if '开盘价' in self.data.columns:
                self.data['open'] = self.data['开盘价']
            if '最高价' in self.data.columns:
                self.data['high'] = self.data['最高价']
            if '最低价' in self.data.columns:
                self.data['low'] = self.data['最低价']
            if '成交量' in self.data.columns:
                self.data['volume'] = self.data['成交量']
            
            logger.info(f"成功加载数据，共{len(self.data)}条记录")
            return True
        except Exception as e:
            logger.error(f"加载数据出错: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标"""
        if self.data is None or self.data.empty:
            logger.error("数据为空，无法计算指标")
            return False
        
        try:
            # 获取配置
            tech_config = self.config.get("technical_indicators", {})
            
            # 计算移动平均线
            ma_periods = tech_config.get("ma_periods", [5, 10, 20, 60])
            for period in ma_periods:
                self.data[f'MA{period}'] = self.data['close'].rolling(window=period).mean()
            
            # 计算RSI
            rsi_period = tech_config.get("rsi_period", 14)
            delta = self.data['close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=rsi_period).mean()
            avg_loss = loss.rolling(window=rsi_period).mean()
            rs = avg_gain / avg_loss
            self.data['RSI'] = 100 - (100 / (1 + rs))
            
            # 计算MACD
            macd_params = tech_config.get("macd_params", [12, 26, 9])
            exp1 = self.data['close'].ewm(span=macd_params[0], adjust=False).mean()
            exp2 = self.data['close'].ewm(span=macd_params[1], adjust=False).mean()
            self.data['MACD'] = exp1 - exp2
            self.data['MACD_SIGNAL'] = self.data['MACD'].ewm(span=macd_params[2], adjust=False).mean()
            self.data['MACD_HIST'] = self.data['MACD'] - self.data['MACD_SIGNAL']
            
            # 计算布林带
            boll_period = tech_config.get("boll_period", 20)
            self.data['BOLL_MIDDLE'] = self.data['close'].rolling(window=boll_period).mean()
            self.data['BOLL_STD'] = self.data['close'].rolling(window=boll_period).std()
            self.data['BOLL_UPPER'] = self.data['BOLL_MIDDLE'] + 2 * self.data['BOLL_STD']
            self.data['BOLL_LOWER'] = self.data['BOLL_MIDDLE'] - 2 * self.data['BOLL_STD']
            
            # 计算ATR
            atr_period = tech_config.get("atr_period", 14)
            high_low = self.data['high'] - self.data['low']
            high_close = np.abs(self.data['high'] - self.data['close'].shift())
            low_close = np.abs(self.data['low'] - self.data['close'].shift())
            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = np.max(ranges, axis=1)
            self.data['ATR'] = true_range.rolling(window=atr_period).mean()
            
            # 计算KDJ
            kd_params = tech_config.get("kd_params", [9, 3, 3])
            low_min = self.data['low'].rolling(window=kd_params[0]).min()
            high_max = self.data['high'].rolling(window=kd_params[0]).max()
            rsv = 100 * ((self.data['close'] - low_min) / (high_max - low_min))
            self.data['K'] = rsv.rolling(window=kd_params[1]).mean()
            self.data['D'] = self.data['K'].rolling(window=kd_params[2]).mean()
            self.data['J'] = 3 * self.data['K'] - 2 * self.data['D']
            
            # 计算DMI
            # 简化版DMI计算
            up_move = self.data['high'].diff()
            down_move = self.data['low'].diff(-1).abs()
            plus_dm = np.where((up_move > down_move) & (up_move > 0), up_move, 0)
            minus_dm = np.where((down_move > up_move) & (down_move > 0), down_move, 0)
            self.data['PLUS_DI'] = pd.Series(plus_dm).rolling(window=14).mean() / self.data['ATR'] * 100
            self.data['MINUS_DI'] = pd.Series(minus_dm).rolling(window=14).mean() / self.data['ATR'] * 100
            self.data['ADX'] = np.abs(self.data['PLUS_DI'] - self.data['MINUS_DI']) / (self.data['PLUS_DI'] + self.data['MINUS_DI']) * 100
            self.data['ADX'] = self.data['ADX'].rolling(window=14).mean()
            
            # 计算CCI
            tp = (self.data['high'] + self.data['low'] + self.data['close']) / 3
            tp_ma = tp.rolling(window=14).mean()
            md = (tp - tp_ma).abs().rolling(window=14).mean()
            self.data['CCI'] = (tp - tp_ma) / (0.015 * md)
            
            # 计算OBV
            obv = [0]
            for i in range(1, len(self.data)):
                if self.data['close'].iloc[i] > self.data['close'].iloc[i-1]:
                    obv.append(obv[-1] + self.data['volume'].iloc[i])
                elif self.data['close'].iloc[i] < self.data['close'].iloc[i-1]:
                    obv.append(obv[-1] - self.data['volume'].iloc[i])
                else:
                    obv.append(obv[-1])
            self.data['OBV'] = pd.Series(obv, index=self.data.index)
            
            logger.info("成功计算技术指标")
            return True
        except Exception as e:
            logger.error(f"计算技术指标出错: {e}")
            return False
