import requests
import pandas as pd
from datetime import datetime, timedelta
import time
import re
import logging

class SinaDataSource:
    """新浪财经数据源"""

    def __init__(self):
        self.logger = logging.getLogger("SinaDataSource")
        self.cache = {}
        self.cache_time = {}
        self.cache_duration = 60  # 缓存时间，单位秒
        self.base_url = "http://hq.sinajs.cn/list="

    def get_realtime_data(self, symbol, exchange=None):
        """获取实时行情数据"""
        current_time = time.time()
        cache_key = symbol

        # 检查缓存是否有效
        if (cache_key in self.cache and
            current_time - self.cache_time.get(cache_key, 0) < self.cache_duration):
            return self.cache[cache_key]

        try:
            self.logger.info(f"从新浪获取{symbol}实时行情数据")

            # 构建新浪API请求URL
            if symbol.startswith('ps'):  # 多晶硅期货
                ticker = f"gf_{symbol}"
            else:
                # 其他期货品种
                exchange_map = {
                    "SHFE": "hf_",  # 上海期货交易所
                    "DCE": "df_",   # 大连商品交易所
                    "CZCE": "zf_",  # 郑州商品交易所
                    "CFFEX": "cf_", # 中国金融期货交易所
                    "GFEX": "gf_",  # 广州期货交易所
                }
                prefix = exchange_map.get(exchange, "hf_")
                ticker = f"{prefix}{symbol}"

            url = f"{self.base_url}{ticker}"
            self.logger.debug(f"请求URL: {url}")

            # 添加请求头，模拟浏览器访问
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'http://finance.sina.com.cn/'
            }
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                text = response.text
                self.logger.debug(f"响应内容: {text}")

                pattern = r'"(.*)"'
                match = re.search(pattern, text)

                if match:
                    data = match.group(1).split(",")
                    if len(data) >= 10:
                        result = {
                            'symbol': symbol,
                            'last_price': float(data[8]),
                            'open': float(data[2]),
                            'high': float(data[3]),
                            'low': float(data[4]),
                            'volume': float(data[9]),
                            'amount': float(data[10]) if len(data) > 10 else 0,
                            'bid_price': float(data[6]),
                            'ask_price': float(data[7]),
                            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        }

                        # 更新缓存
                        self.cache[cache_key] = result
                        self.cache_time[cache_key] = current_time

                        self.logger.info(f"成功获取{symbol}实时数据")
                        return result
                    else:
                        self.logger.warning(f"解析{symbol}数据失败，数据格式不正确")
                else:
                    self.logger.warning(f"解析{symbol}数据失败，未找到匹配内容")
            else:
                self.logger.warning(f"请求{symbol}数据失败，状态码: {response.status_code}")

            return None
        except Exception as e:
            self.logger.error(f"获取{symbol}实时数据出错: {e}")
            return None

    def get_historical_data(self, symbol, period='daily', start_date=None, end_date=None):
        """获取历史数据（新浪API不支持直接获取历史数据）"""
        self.logger.warning("新浪数据源不支持直接获取历史数据，请使用AKShare数据源")
        return None
